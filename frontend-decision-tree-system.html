<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>决策树系统 - Decision Tree System</title>
    <style>
        :root {
            --primary-color: #4a6fa5;
            --secondary-color: #e3f2fd;
            --accent-color: #ff6b6b;
            --text-color: #333;
            --light-text: #666;
            --border-color: #ddd;
            --highlight-color: #f5f5f5;
            --success-color: #4caf50;
            --warning-color: #ff9800;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #f9f9f9;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            min-height: calc(100vh - 40px);
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .header-title {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .api-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #ccc;
        }
        
        .status-connected {
            background-color: var(--success-color);
        }
        
        main {
            display: flex;
            flex: 1;
            gap: 20px;
        }
        
        .sidebar {
            width: 300px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            padding: 20px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .tree-list {
            flex: 1;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .tree-item {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s;
            border-left: 3px solid transparent;
        }
        
        .tree-item:hover {
            background-color: var(--secondary-color);
        }
        
        .tree-item.active {
            background-color: var(--secondary-color);
            border-left-color: var(--primary-color);
        }
        
        .tree-item-title {
            font-weight: 500;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .tree-item-date {
            font-size: 12px;
            color: var(--light-text);
        }
        
        .content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .tree-view {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            padding: 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .tree-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .tree-title {
            font-size: 20px;
            font-weight: bold;
        }
        
        .tree-actions {
            display: flex;
            gap: 10px;
        }
        
        .node-container {
            flex: 1;
            overflow-y: auto;
            padding: 10px 0;
        }
        
        .decision-node {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: white;
            transition: all 0.2s;
        }
        
        .decision-node:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .decision-node.selected {
            border-color: var(--primary-color);
            background-color: var(--secondary-color);
        }
        
        .node-question {
            font-weight: 500;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .node-decision {
            margin-bottom: 10px;
        }
        
        .node-probability {
            font-size: 14px;
            color: var(--light-text);
            margin-bottom: 15px;
        }
        
        .probability-bar {
            height: 6px;
            background-color: #eee;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .probability-fill {
            height: 100%;
            background-color: var(--primary-color);
        }
        
        .node-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        
        .children-container {
            margin-left: 30px;
            position: relative;
        }
        
        .children-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -15px;
            width: 2px;
            height: 100%;
            background-color: var(--border-color);
        }
        
        .input-form {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }
        
        .form-title {
            font-weight: 500;
            margin-bottom: 10px;
        }
        
        .input-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        input, select, textarea, button {
            padding: 10px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            font-family: inherit;
        }
        
        textarea {
            width: 100%;
            min-height: 80px;
            resize: vertical;
        }
        
        button {
            cursor: pointer;
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 15px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        button:hover {
            background-color: #3a5a85;
        }
        
        button.secondary {
            background-color: #f5f5f5;
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }
        
        button.secondary:hover {
            background-color: #e5e5e5;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 20px;
            color: var(--light-text);
        }
        
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-top-color: var(--primary-color);
            border-radius: 50%;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .api-setup-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .modal-content {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: bold;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: var(--light-text);
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .error-message {
            color: var(--accent-color);
            font-size: 14px;
            margin-top: 5px;
        }
        
        .success-message {
            color: var(--success-color);
            font-size: 14px;
            margin-top: 5px;
        }
        
        .export-import-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }
        
        .visualizer {
            margin-top: 20px;
            overflow: auto;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            background-color: var(--highlight-color);
        }
        
        .tooltip {
            position: relative;
            display: inline-block;
            margin-left: 5px;
            cursor: help;
        }
        
        .tooltip-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: var(--light-text);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        
        .tooltip-text {
            visibility: hidden;
            width: 250px;
            background-color: #555;
            color: #fff;
            text-align: left;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -125px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        
        @media (max-width: 768px) {
            main {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-title">决策树系统 | Decision Tree System</div>
            <div class="api-status">
                <div class="status-indicator" id="apiStatusIndicator"></div>
                <span id="apiStatusText">API 未配置</span>
                <button class="secondary" id="setupApiBtn">配置 API</button>
            </div>
        </header>
        
        <main>
            <div class="sidebar">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>我的决策树</h3>
                    <button id="newTreeBtn">新建</button>
                </div>
                <div class="tree-list" id="treeList">
                    <!-- 决策树列表将动态插入 -->
                </div>
            </div>
            
            <div class="content">
                <div class="tree-view" id="treeView">
                    <div id="emptyState">
                        <div style="text-align: center; padding: 40px 0;">
                            <h3>欢迎使用决策树系统</h3>
                            <p style="margin: 20px 0;">创建新的决策树或从左侧选择已有的决策树</p>
                            <button id="emptyStateNewBtn">创建新决策树</button>
                        </div>
                    </div>
                    
                    <div id="treeContent" style="display: none; height: 100%;">
                        <div class="tree-header">
                            <div class="tree-title" id="currentTreeTitle">决策树标题</div>
                            <div class="tree-actions">
                                <button class="secondary" id="exportTreeBtn">导出</button>
                                <button class="secondary" id="deleteTreeBtn">删除</button>
                            </div>
                        </div>
                        
                        <div class="node-container" id="nodeContainer">
                            <!-- 决策节点将动态插入 -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- API 配置模态框 -->
    <div class="api-setup-modal" id="apiSetupModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">配置 AI API</div>
                <button class="close-btn" id="closeApiModal">&times;</button>
            </div>
            
            <form id="apiConfigForm">
                <div class="form-group">
                    <label for="apiProvider">API 提供商</label>
                    <select id="apiProvider" name="apiProvider">
                        <option value="anthropic">Anthropic (Claude)</option>
                        <option value="openai">OpenAI (GPT)</option>
                        <option value="custom">自定义</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="apiUrl">API URL</label>
                    <input type="text" id="apiUrl" name="apiUrl" required>
                    <div class="tooltip">
                        <span class="tooltip-icon">?</span>
                        <span class="tooltip-text">
                            Anthropic: https://api.anthropic.com/v1/messages<br>
                            OpenAI: https://api.openai.com/v1/chat/completions
                        </span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="apiKey">API Key</label>
                    <input type="password" id="apiKey" name="apiKey" required>
                    <div class="tooltip">
                        <span class="tooltip-icon">?</span>
                        <span class="tooltip-text">
                            您的 API 密钥将安全地存储在浏览器的本地存储中，不会发送到任何服务器。
                        </span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="modelName">模型名称</label>
                    <input type="text" id="modelName" name="modelName" required>
                    <div class="tooltip">
                        <span class="tooltip-icon">?</span>
                        <span class="tooltip-text">
                            例如：claude-3-opus-20240229, claude-3-sonnet-20240229, gpt-4-turbo, gpt-3.5-turbo 等
                        </span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="decisionsCount">生成决策数量</label>
                    <input type="number" id="decisionsCount" name="decisionsCount" min="1" max="10" value="5">
                </div>
                
                <button type="submit">保存配置</button>
                <div id="apiConfigMessage"></div>
                
                <div class="form-group" style="margin-top: 15px;">
                    <button type="button" id="testApiBtn" class="secondary">测试 API 连接</button>
                    <div id="apiTestResult"></div>
                </div>
            </form>
            
            <div class="export-import-section">
                <h4>导入/导出 API 配置</h4>
                <p style="font-size: 14px; margin: 10px 0;">您可以导出当前 API 配置以备份，或导入之前保存的配置。</p>
                <div style="display: flex; gap: 10px; margin-top: 10px;">
                    <button id="exportConfigBtn" class="secondary">导出配置</button>
                    <button id="importConfigBtn" class="secondary">导入配置</button>
                    <input type="file" id="importConfigFile" style="display: none;">
                </div>
            </div>
        </div>
    </div>
    
    <!-- 新建决策树模态框 -->
    <div class="api-setup-modal" id="newTreeModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">创建新决策树</div>
                <button class="close-btn" id="closeNewTreeModal">&times;</button>
            </div>
            
            <form id="newTreeForm">
                <div class="form-group">
                    <label for="newTreeQuestion">问题</label>
                    <textarea id="newTreeQuestion" name="newTreeQuestion" placeholder="请输入您的问题..." required></textarea>
                </div>
                
                <button type="submit">创建</button>
                <div id="newTreeMessage"></div>
            </form>
        </div>
    </div>
    
    <!-- 添加后续问题模态框 -->
    <div class="api-setup-modal" id="followUpModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">添加后续问题</div>
                <button class="close-btn" id="closeFollowUpModal">&times;</button>
            </div>
            
            <form id="followUpForm">
                <div class="form-group">
                    <label for="selectedDecision">为以下决策添加后续问题：</label>
                    <div id="selectedDecision" style="padding: 10px; background-color: var(--secondary-color); border-radius: 4px; margin-bottom: 10px;"></div>
                </div>
                
                <div class="form-group">
                    <label for="followUpQuestion">后续问题</label>
                    <textarea id="followUpQuestion" name="followUpQuestion" placeholder="请输入后续问题..." required></textarea>
                </div>
                
                <button type="submit">提交</button>
                <div id="followUpMessage"></div>
            </form>
        </div>
    </div>
    
    <!-- 导出决策树模态框 -->
    <div class="api-setup-modal" id="exportModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">导出决策树</div>
                <button class="close-btn" id="closeExportModal">&times;</button>
            </div>
            
            <div style="margin-bottom: 20px;">
                <p>您可以将决策树导出为 JSON 文件以备份或分享。</p>
            </div>
            
            <div style="display: flex; gap: 10px;">
                <button id="downloadJsonBtn">下载 JSON</button>
                <button id="copyJsonBtn">复制 JSON</button>
            </div>
            
            <div class="visualizer">
                <pre id="exportContent" style="white-space: pre-wrap; word-break: break-all;"></pre>
            </div>
        </div>
    </div>
    
    <!-- 导入决策树模态框 -->
    <div class="api-setup-modal" id="importModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">导入决策树</div>
                <button class="close-btn" id="closeImportModal">&times;</button>
            </div>
            
            <div style="margin-bottom: 20px;">
                <p>请选择之前导出的决策树 JSON 文件或粘贴 JSON 内容。</p>
            </div>
            
            <div class="form-group">
                <label for="importFile">从文件导入：</label>
                <input type="file" id="importFile" accept=".json">
            </div>
            
            <div class="form-group">
                <label for="importJson">或粘贴 JSON：</label>
                <textarea id="importJson" rows="8" placeholder="粘贴 JSON 内容..."></textarea>
            </div>
            
            <button id="importTreeBtn">导入</button>
            <div id="importMessage"></div>
        </div>
    </div>

    <script>
        // UUID 生成函数
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
        
        // 决策节点类
        class DecisionNode {
            constructor(question, decision, probability, parentId = null, context = []) {
                this.id = generateUUID();
                this.question = question;
                this.decision = decision;
                this.probability = probability;
                this.parentId = parentId;
                this.children = [];
                this.createdAt = new Date().toISOString();
                this.context = context || [];
            }
        }
        
        // 决策树类
        class DecisionTree {
            constructor(rootQuestion, apiConfig) {
                this.id = generateUUID();
                this.nodes = {};
                this.rootId = null;
                this.apiConfig = apiConfig;
                this.createdAt = new Date().toISOString();
                this.updatedAt = this.createdAt;
                
                if (rootQuestion) {
                    this.createRoot(rootQuestion);
                }
            }
            
            async createRoot(question) {
                try {
                    const decisions = await this._generateDecisions(question, null);
                    
                    if (decisions && decisions.length > 0) {
                        // 创建根节点
                        const rootDecision = decisions[0];
                        const rootNode = new DecisionNode(
                            question,
                            rootDecision.decision,
                            rootDecision.probability,
                            null,
                            [
                                { role: "user", content: question },
                                { role: "assistant", content: rootDecision.decision }
                            ]
                        );
                        
                        this.rootId = rootNode.id;
                        this.nodes[rootNode.id] = rootNode;
                        
                        // 为其他决策创建子节点
                        for (let i = 1; i < decisions.length; i++) {
                            const decision = decisions[i];
                            const childNode = new DecisionNode(
                                question,
                                decision.decision,
                                decision.probability,
                                rootNode.id,
                                [
                                    { role: "user", content: question },
                                    { role: "assistant", content: decision.decision }
                                ]
                            );
                            
                            this.nodes[childNode.id] = childNode;
                            rootNode.children.push(childNode.id);
                        }
                        
                        this.updatedAt = new Date().toISOString();
                        return this.rootId;
                    }
                    
                    throw new Error("无法生成根决策");
                } catch (error) {
                    console.error("创建根节点错误:", error);
                    throw error;
                }
            }
            
            async addFollowUp(parentId, followUpQuestion) {
                if (!this.nodes[parentId]) {
                    throw new Error(`节点ID ${parentId} 不存在`);
                }
                
                const parentNode = this.nodes[parentId];
                
                // 更新上下文
                const context = [...parentNode.context];
                context.push({ role: "user", content: followUpQuestion });
                
                try {
                    // 生成子决策
                    const decisions = await this._generateDecisions(followUpQuestion, context);
                    
                    const newChildIds = [];
                    
                    if (decisions && decisions.length > 0) {
                        for (const decision of decisions) {
                            // 更新上下文，添加AI的回复
                            const newContext = [...context];
                            newContext.push({ role: "assistant", content: decision.decision });
                            
                            // 创建子节点
                            const childNode = new DecisionNode(
                                followUpQuestion,
                                decision.decision,
                                decision.probability,
                                parentId,
                                newContext
                            );
                            
                            this.nodes[childNode.id] = childNode;
                            parentNode.children.push(childNode.id);
                            newChildIds.push(childNode.id);
                        }
                        
                        this.updatedAt = new Date().toISOString();
                        return newChildIds;
                    }
                    
                    throw new Error("无法生成子决策");
                } catch (error) {
                    console.error("添加后续问题错误:", error);
                    throw error;
                }
            }
            
            async _generateDecisions(question, context) {
                const apiConfig = this.apiConfig;
                
                if (!apiConfig || !apiConfig.apiUrl || !apiConfig.apiKey || !apiConfig.modelName) {
                    throw new Error("API 配置不完整");
                }
                
                const apiProvider = apiConfig.apiProvider || "anthropic";
                const decisionsCount = apiConfig.decisionsCount || 5;
                
                const systemPrompt = `
                你是一个决策生成助手。对于给定的问题，请生成${decisionsCount}个可能的决策或解决方案，按照概率从高到低排序。
                对于每个决策，提供以下信息：
                1. 决策内容
                2. 概率评估（0-1之间的数值）
                
                以JSON格式返回，格式如下：
                [
                    {"decision": "决策1内容", "probability": 0.8},
                    {"decision": "决策2内容", "probability": 0.7},
                    ...
                ]
                `;
                
                try {
                    let response;
                    
                    if (apiProvider === "anthropic") {
                        response = await this._callAnthropicAPI(question, context, apiConfig, systemPrompt);
                    } else if (apiProvider === "openai") {
                        response = await this._callOpenAIAPI(question, context, apiConfig, systemPrompt);
                    } else {
                        response = await this._callGenericAPI(question, context, apiConfig, systemPrompt);
                    }
                    
                    return response;
                } catch (error) {
                    console.error("API 调用错误:", error);
                    throw error;
                }
            }
            
            async _callAnthropicAPI(question, context, apiConfig, systemPrompt) {
                const apiUrl = apiConfig.apiUrl;
                const apiKey = apiConfig.apiKey;
                const modelName = apiConfig.modelName;
                
                // 构建消息
                let messages = [];
                if (context && context.length > 0) {
                    messages = [...context];
                } else {
                    messages.push({ role: "user", content: question });
                }
                
                // 构建请求
                const requestData = {
                    model: modelName,
                    system: systemPrompt,
                    messages: messages,
                    max_tokens: 2000
                };
                
                // 发送请求
                const response = await fetch(apiUrl, {
                    method: "POST",
                    headers: {
                        "X-API-Key": apiKey,
                        "Content-Type": "application/json",
                        "Anthropic-Version": "2023-06-01"
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => null);
                    throw new Error(`API 请求失败: ${response.status} ${errorData ? JSON.stringify(errorData) : response.statusText}`);
                }
                
                const responseData = await response.json();
                
                // 解析响应
                if (responseData.content && Array.isArray(responseData.content)) {
                    for (const item of responseData.content) {
                        if (item.type === "text") {
                            try {
                                // 尝试从文本中提取 JSON
                                const text = item.text.trim();
                                
                                // 查找 JSON 数组
                                const jsonMatch = text.match(/\[\s*\{.*\}\s*\]/s);
                                if (jsonMatch) {
                                    return JSON.parse(jsonMatch[0]);
                                }
                                
                                // 直接尝试解析整个文本
                                return JSON.parse(text);
                            } catch (e) {
                                console.error("JSON 解析错误:", e, item.text);
                            }
                        }
                    }
                }
                
                throw new Error("无法从 API 响应中提取决策");
            }
            
            async _callOpenAIAPI(question, context, apiConfig, systemPrompt) {
                const apiUrl = apiConfig.apiUrl;
                const apiKey = apiConfig.apiKey;
                const modelName = apiConfig.modelName;
                
                // 构建消息
                const messages = [
                    { role: "system", content: systemPrompt }
                ];
                
                if (context && context.length > 0) {
                    messages.push(...context);
                } else {
                    messages.push({ role: "user", content: question });
                }
                
                // 构建请求
                const requestData = {
                    model: modelName,
                    messages: messages,
                    max_tokens: 2000
                };
                
                // 发送请求
                const response = await fetch(apiUrl, {
                    method: "POST",
                    headers: {
                        "Authorization": `Bearer ${apiKey}`,
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => null);
                    throw new Error(`API 请求失败: ${response.status} ${errorData ? JSON.stringify(errorData) : response.statusText}`);
                }
                
                const responseData = await response.json();
                
                // 解析响应
                if (responseData.choices && responseData.choices.length > 0) {
                    const content = responseData.choices[0].message.content;
                    
                    try {
                        // 尝试从文本中提取 JSON
                        const text = content.trim();
                        
                        // 查找 JSON 数组
                        const jsonMatch = text.match(/\[\s*\{.*\}\s*\]/s);
                        if (jsonMatch) {
                            return JSON.parse(jsonMatch[0]);
                        }
                        
                        // 直接尝试解析整个文本
                        return JSON.parse(text);
                    } catch (e) {
                        console.error("JSON 解析错误:", e, content);
                    }
                }
                
                throw new Error("无法从 API 响应中提取决策");
            }
            
            async _callGenericAPI(question, context, apiConfig, systemPrompt) {
                const apiUrl = apiConfig.apiUrl;
                const apiKey = apiConfig.apiKey;
                const modelName = apiConfig.modelName;
                
                // 构建消息 (通用格式，类似 OpenAI)
                const messages = [
                    { role: "system", content: systemPrompt }
                ];
                
                if (context && context.length > 0) {
                    messages.push(...context);
                } else {
                    messages.push({ role: "user", content: question });
                }
                
                // 构建请求
                const requestData = {
                    model: modelName,
                    messages: messages,
                    max_tokens: 2000
                };
                
                // 发送请求
                const response = await fetch(apiUrl, {
                    method: "POST",
                    headers: {
                        "Authorization": `Bearer ${apiKey}`,
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => null);
                    throw new Error(`API 请求失败: ${response.status} ${errorData ? JSON.stringify(errorData) : response.statusText}`);
                }
                
                const responseData = await response.json();
                
                // 尝试多种响应格式解析
                // 1. OpenAI 格式
                if (responseData.choices && responseData.choices.length > 0 && responseData.choices[0].message) {
                    const content = responseData.choices[0].message.content;
                    try {
                        return this._extractJsonFromText(content);
                    } catch (e) {
                        console.error("OpenAI 格式解析错误:", e);
                    }
                }
                
                // 2. Anthropic 格式
                if (responseData.content && Array.isArray(responseData.content)) {
                    for (const item of responseData.content) {
                        if (item.type === "text") {
                            try {
                                return this._extractJsonFromText(item.text);
                            } catch (e) {
                                console.error("Anthropic 格式解析错误:", e);
                            }
                        }
                    }
                }
                
                // 3. 通用响应对象格式
                try {
                    for (const key in responseData) {
                        if (typeof responseData[key] === 'string') {
                            try {
                                return this._extractJsonFromText(responseData[key]);
                            } catch (e) {
                                continue;
                            }
                        }
                    }
                } catch (e) {
                    console.error("通用格式解析错误:", e);
                }
                
                throw new Error("无法从 API 响应中提取决策");
            }
            
            _extractJsonFromText(text) {
                text = text.trim();
                
                // 查找 JSON 数组
                const jsonMatch = text.match(/\[\s*\{.*\}\s*\]/s);
                if (jsonMatch) {
                    return JSON.parse(jsonMatch[0]);
                }
                
                // 直接尝试解析整个文本
                return JSON.parse(text);
            }
            
            getNode(nodeId) {
                return this.nodes[nodeId];
            }
            
            getParentNode(nodeId) {
                const node = this.nodes[nodeId];
                if (node && node.parentId) {
                    return this.nodes[node.parentId];
                }
                return null;
            }
            
            getPathToRoot(nodeId) {
                const path = [];
                let currentNode = this.nodes[nodeId];
                
                while (currentNode) {
                    path.unshift(currentNode.id);
                    if (currentNode.parentId) {
                        currentNode = this.nodes[currentNode.parentId];
                    } else {
                        break;
                    }
                }
                
                return path;
            }
            
            toJSON() {
                return {
                    id: this.id,
                    rootId: this.rootId,
                    nodes: this.nodes,
                    createdAt: this.createdAt,
                    updatedAt: this.updatedAt
                };
            }
            
            static fromJSON(data, apiConfig) {
                const tree = new DecisionTree(null, apiConfig);
                tree.id = data.id;
                tree.rootId = data.rootId;
                tree.nodes = data.nodes;
                tree.createdAt = data.createdAt;
                tree.updatedAt = data.updatedAt;
                return tree;
            }
        }
        
        // 决策树系统类
        class DecisionTreeSystem {
            constructor() {
                this.trees = {};
                this.apiConfig = null;
                this.loadFromLocalStorage();
            }
            
            loadFromLocalStorage() {
                // 加载 API 配置
                const apiConfigStr = localStorage.getItem('decisionTreeApiConfig');
                if (apiConfigStr) {
                    try {
                        this.apiConfig = JSON.parse(apiConfigStr);
                    } catch (e) {
                        console.error("API 配置加载错误:", e);
                    }
                }
                
                // 加载决策树
                const treeIdsStr = localStorage.getItem('decisionTreeIds');
                if (treeIdsStr) {
                    try {
                        const treeIds = JSON.parse(treeIdsStr);
                        
                        for (const treeId of treeIds) {
                            const treeDataStr = localStorage.getItem(`decisionTree_${treeId}`);
                            if (treeDataStr) {
                                try {
                                    const treeData = JSON.parse(treeDataStr);
                                    this.trees[treeId] = DecisionTree.fromJSON(treeData, this.apiConfig);
                                } catch (e) {
                                    console.error(`决策树 ${treeId} 加载错误:`, e);
                                }
                            }
                        }
                    } catch (e) {
                        console.error("决策树 ID 列表加载错误:", e);
                    }
                }
            }
            
            saveToLocalStorage() {
                // 保存 API 配置
                if (this.apiConfig) {
                    localStorage.setItem('decisionTreeApiConfig', JSON.stringify(this.apiConfig));
                }
                
                // 保存决策树 ID 列表
                const treeIds = Object.keys(this.trees);
                localStorage.setItem('decisionTreeIds', JSON.stringify(treeIds));
                
                // 保存每个决策树
                for (const treeId in this.trees) {
                    localStorage.setItem(`decisionTree_${treeId}`, JSON.stringify(this.trees[treeId]));
                }
            }
            
            setApiConfig(config) {
                this.apiConfig = config;
                
                // 更新所有树的 API 配置
                for (const treeId in this.trees) {
                    this.trees[treeId].apiConfig = config;
                }
                
                this.saveToLocalStorage();
            }
            
            getApiConfig() {
                return this.apiConfig;
            }
            
            async createTree(rootQuestion) {
                if (!this.apiConfig) {
                    throw new Error("未配置 API");
                }
                
                const tree = new DecisionTree(rootQuestion, this.apiConfig);
                await tree.createRoot(rootQuestion);
                
                this.trees[tree.id] = tree;
                this.saveToLocalStorage();
                
                return tree.id;
            }
            
            async addFollowUp(treeId, nodeId, question) {
                const tree = this.trees[treeId];
                if (!tree) {
                    throw new Error(`决策树 ${treeId} 不存在`);
                }
                
                const childIds = await tree.addFollowUp(nodeId, question);
                this.saveToLocalStorage();
                
                return childIds;
            }
            
            getTree(treeId) {
                return this.trees[treeId];
            }
            
            deleteTree(treeId) {
                if (this.trees[treeId]) {
                    delete this.trees[treeId];
                    localStorage.removeItem(`decisionTree_${treeId}`);
                    this.saveToLocalStorage();
                    return true;
                }
                return false;
            }
            
            getAllTrees() {
                const result = [];
                
                for (const treeId in this.trees) {
                    const tree = this.trees[treeId];
                    const rootNode = tree.nodes[tree.rootId];
                    
                    if (rootNode) {
                        result.push({
                            id: treeId,
                            question: rootNode.question,
                            createdAt: tree.createdAt,
                            updatedAt: tree.updatedAt
                        });
                    }
                }
                
                // 按更新时间排序，最新的在前面
                result.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
                
                return result;
            }
            
            importTree(treeData) {
                try {
                    const tree = DecisionTree.fromJSON(treeData, this.apiConfig);
                    this.trees[tree.id] = tree;
                    this.saveToLocalStorage();
                    return tree.id;
                } catch (e) {
                    console.error("导入决策树错误:", e);
                    throw new Error("导入决策树失败: " + e.message);
                }
            }
            
            exportTree(treeId) {
                const tree = this.trees[treeId];
                if (!tree) {
                    throw new Error(`决策树 ${treeId} 不存在`);
                }
                
                return JSON.stringify(tree, null, 2);
            }
            
            async testApiConnection(apiConfig) {
                // 创建一个简单的问题来测试 API 连接
                const testQuestion = "测试问题: 1+1=?";
                
                try {
                    const tempTree = new DecisionTree(null, apiConfig);
                    const decisions = await tempTree._generateDecisions(testQuestion, null);
                    
                    return {
                        success: true,
                        message: "API 连接成功",
                        data: decisions
                    };
                } catch (error) {
                    return {
                        success: false,
                        message: `API 连接失败: ${error.message}`,
                        error: error
                    };
                }
            }
        }
        
        // 应用类 - 管理 UI 交互
        class DecisionTreeApp {
            constructor() {
                this.system = new DecisionTreeSystem();
                this.currentTreeId = null;
                this.selectedNodeId = null;
                
                this.initUI();
                this.renderTreeList();
                this.updateApiStatus();
            }
            
            initUI() {
                // API 配置模态框
                const apiSetupModal = document.getElementById('apiSetupModal');
                const apiConfigForm = document.getElementById('apiConfigForm');
                const apiProvider = document.getElementById('apiProvider');
                const apiUrl = document.getElementById('apiUrl');
                const apiKey = document.getElementById('apiKey');
                const modelName = document.getElementById('modelName');
                const decisionsCount = document.getElementById('decisionsCount');
                const setupApiBtn = document.getElementById('setupApiBtn');
                const closeApiModal = document.getElementById('closeApiModal');
                const testApiBtn = document.getElementById('testApiBtn');
                const apiTestResult = document.getElementById('apiTestResult');
                const apiConfigMessage = document.getElementById('apiConfigMessage');
                const exportConfigBtn = document.getElementById('exportConfigBtn');
                const importConfigBtn = document.getElementById('importConfigBtn');
                const importConfigFile = document.getElementById('importConfigFile');
                
                // 加载已保存的 API 配置
                const apiConfig = this.system.getApiConfig();
                if (apiConfig) {
                    if (apiConfig.apiProvider) apiProvider.value = apiConfig.apiProvider;
                    if (apiConfig.apiUrl) apiUrl.value = apiConfig.apiUrl;
                    if (apiConfig.apiKey) apiKey.value = apiConfig.apiKey;
                    if (apiConfig.modelName) modelName.value = apiConfig.modelName;
                    if (apiConfig.decisionsCount) decisionsCount.value = apiConfig.decisionsCount;
                } else {
                    // 默认值
                    apiProvider.value = "anthropic";
                    apiUrl.value = "https://api.anthropic.com/v1/messages";
                    modelName.value = "claude-3-haiku-20240307";
                    decisionsCount.value = "5";
                }
                
                // API 提供商变更事件
                apiProvider.addEventListener('change', () => {
                    if (apiProvider.value === "anthropic") {
                        apiUrl.value = "https://api.anthropic.com/v1/messages";
                    } else if (apiProvider.value === "openai") {
                        apiUrl.value = "https://api.openai.com/v1/chat/completions";
                    } else {
                        apiUrl.value = "";
                    }
                });
                
                // 配置 API 按钮点击
                setupApiBtn.addEventListener('click', () => {
                    apiSetupModal.style.display = 'flex';
                });
                
                // 关闭模态框
                closeApiModal.addEventListener('click', () => {
                    apiSetupModal.style.display = 'none';
                });
                
                // API 配置表单提交
                apiConfigForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    
                    const config = {
                        apiProvider: apiProvider.value,
                        apiUrl: apiUrl.value,
                        apiKey: apiKey.value,
                        modelName: modelName.value,
                        decisionsCount: parseInt(decisionsCount.value)
                    };
                    
                    try {
                        this.system.setApiConfig(config);
                        apiConfigMessage.textContent = "API 配置已保存";
                        apiConfigMessage.className = "success-message";
                        
                        setTimeout(() => {
                            apiConfigMessage.textContent = "";
                        }, 3000);
                        
                        this.updateApiStatus();
                    } catch (error) {
                        apiConfigMessage.textContent = `保存失败: ${error.message}`;
                        apiConfigMessage.className = "error-message";
                    }
                });
                
                // 测试 API 连接
                testApiBtn.addEventListener('click', async () => {
                    apiTestResult.textContent = "正在测试 API 连接...";
                    apiTestResult.className = "";
                    
                    const config = {
                        apiProvider: apiProvider.value,
                        apiUrl: apiUrl.value,
                        apiKey: apiKey.value,
                        modelName: modelName.value,
                        decisionsCount: parseInt(decisionsCount.value)
                    };
                    
                    try {
                        const result = await this.system.testApiConnection(config);
                        
                        if (result.success) {
                            apiTestResult.textContent = `${result.message} ✓`;
                            apiTestResult.className = "success-message";
                        } else {
                            apiTestResult.textContent = result.message;
                            apiTestResult.className = "error-message";
                        }
                    } catch (error) {
                        apiTestResult.textContent = `测试失败: ${error.message}`;
                        apiTestResult.className = "error-message";
                    }
                });
                
                // 导出 API 配置
                exportConfigBtn.addEventListener('click', () => {
                    const config = this.system.getApiConfig();
                    if (!config) {
                        alert('没有 API 配置可导出');
                        return;
                    }
                    
                    const configData = JSON.stringify(config, null, 2);
                    const blob = new Blob([configData], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'decision_tree_api_config.json';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                });
                
                // 导入 API 配置
                importConfigBtn.addEventListener('click', () => {
                    importConfigFile.click();
                });
                
                importConfigFile.addEventListener('change', (e) => {
                    const file = e.target.files[0];
                    if (!file) return;
                    
                    const reader = new FileReader();
                    reader.onload = (event) => {
                        try {
                            const config = JSON.parse(event.target.result);
                            
                            // 更新表单
                            apiProvider.value = config.apiProvider || "anthropic";
                            apiUrl.value = config.apiUrl || "";
                            apiKey.value = config.apiKey || "";
                            modelName.value = config.modelName || "";
                            decisionsCount.value = config.decisionsCount || 5;
                            
                            apiConfigMessage.textContent = "API 配置已导入";
                            apiConfigMessage.className = "success-message";
                            
                            setTimeout(() => {
                                apiConfigMessage.textContent = "";
                            }, 3000);
                        } catch (error) {
                            apiConfigMessage.textContent = `导入失败: ${error.message}`;
                            apiConfigMessage.className = "error-message";
                        }
                    };
                    reader.readAsText(file);
                });
                
                // 新建决策树模态框
                const newTreeModal = document.getElementById('newTreeModal');
                const newTreeForm = document.getElementById('newTreeForm');
                const newTreeQuestion = document.getElementById('newTreeQuestion');
                const closeNewTreeModal = document.getElementById('closeNewTreeModal');
                const newTreeBtn = document.getElementById('newTreeBtn');
                const emptyStateNewBtn = document.getElementById('emptyStateNewBtn');
                const newTreeMessage = document.getElementById('newTreeMessage');
                
                // 新建决策树按钮点击
                newTreeBtn.addEventListener('click', () => {
                    if (!this.system.getApiConfig()) {
                        alert('请先配置 API');
                        apiSetupModal.style.display = 'flex';
                        return;
                    }
                    
                    newTreeQuestion.value = '';
                    newTreeMessage.textContent = '';
                    newTreeModal.style.display = 'flex';
                });
                
                emptyStateNewBtn.addEventListener('click', () => {
                    newTreeBtn.click();
                });
                
                // 关闭模态框
                closeNewTreeModal.addEventListener('click', () => {
                    newTreeModal.style.display = 'none';
                });
                
                // 新建决策树表单提交
                newTreeForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    
                    const question = newTreeQuestion.value.trim();
                    if (!question) return;
                    
                    newTreeMessage.textContent = "正在创建决策树...";
                    newTreeMessage.className = "";
                    
                    try {
                        const treeId = await this.system.createTree(question);
                        
                        newTreeMessage.textContent = "决策树创建成功!";
                        newTreeMessage.className = "success-message";
                        
                        setTimeout(() => {
                            newTreeModal.style.display = 'none';
                            this.renderTreeList();
                            this.loadTree(treeId);
                        }, 1000);
                    } catch (error) {
                        newTreeMessage.textContent = `创建失败: ${error.message}`;
                        newTreeMessage.className = "error-message";
                    }
                });
                
                // 添加后续问题模态框
                const followUpModal = document.getElementById('followUpModal');
                const followUpForm = document.getElementById('followUpForm');
                const selectedDecision = document.getElementById('selectedDecision');
                const followUpQuestion = document.getElementById('followUpQuestion');
                const closeFollowUpModal = document.getElementById('closeFollowUpModal');
                const followUpMessage = document.getElementById('followUpMessage');
                
                // 关闭模态框
                closeFollowUpModal.addEventListener('click', () => {
                    followUpModal.style.display = 'none';
                });
                
                // 添加后续问题表单提交
                followUpForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    
                    const question = followUpQuestion.value.trim();
                    if (!question) return;
                    
                    followUpMessage.textContent = "正在生成子决策...";
                    followUpMessage.className = "";
                    
                    try {
                        await this.system.addFollowUp(this.currentTreeId, this.selectedNodeId, question);
                        
                        followUpMessage.textContent = "子决策生成成功!";
                        followUpMessage.className = "success-message";
                        
                        setTimeout(() => {
                            followUpModal.style.display = 'none';
                            this.renderTree();
                        }, 1000);
                    } catch (error) {
                        followUpMessage.textContent = `生成失败: ${error.message}`;
                        followUpMessage.className = "error-message";
                    }
                });
                
                // 导出决策树模态框
                const exportModal = document.getElementById('exportModal');
                const closeExportModal = document.getElementById('closeExportModal');
                const downloadJsonBtn = document.getElementById('downloadJsonBtn');
                const copyJsonBtn = document.getElementById('copyJsonBtn');
                const exportContent = document.getElementById('exportContent');
                const exportTreeBtn = document.getElementById('exportTreeBtn');
                
                // 导出决策树按钮点击
                exportTreeBtn.addEventListener('click', () => {
                    if (!this.currentTreeId) return;
                    
                    try {
                        const treeJson = this.system.exportTree(this.currentTreeId);
                        exportContent.textContent = treeJson;
                        exportModal.style.display = 'flex';
                    } catch (error) {
                        alert(`导出失败: ${error.message}`);
                    }
                });
                
                // 关闭模态框
                closeExportModal.addEventListener('click', () => {
                    exportModal.style.display = 'none';
                });
                
                // 下载 JSON
                downloadJsonBtn.addEventListener('click', () => {
                    if (!exportContent.textContent) return;
                    
                    const tree = this.system.getTree(this.currentTreeId);
                    const rootNode = tree.nodes[tree.rootId];
                    const fileName = `decision_tree_${rootNode.question.slice(0, 20).replace(/[^\w\s]/gi, '')}.json`;
                    
                    const blob = new Blob([exportContent.textContent], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                });
                
                // 复制 JSON
                copyJsonBtn.addEventListener('click', () => {
                    if (!exportContent.textContent) return;
                    
                    navigator.clipboard.writeText(exportContent.textContent)
                        .then(() => {
                            alert('JSON 已复制到剪贴板');
                        })
                        .catch((error) => {
                            alert(`复制失败: ${error.message}`);
                        });
                });
                
                // 导入决策树
                const importModal = document.getElementById('importModal');
                const closeImportModal = document.getElementById('closeImportModal');
                const importFile = document.getElementById('importFile');
                const importJson = document.getElementById('importJson');
                const importTreeBtn = document.getElementById('importTreeBtn');
                const importMessage = document.getElementById('importMessage');
                
                // 创建一个导入按钮
                const importTreeHeaderBtn = document.createElement('button');
                importTreeHeaderBtn.className = 'secondary';
                importTreeHeaderBtn.textContent = '导入';
                importTreeHeaderBtn.style.marginRight = '10px';
                const treeActions = document.querySelector('.tree-actions');
                treeActions.insertBefore(importTreeHeaderBtn, treeActions.firstChild);
                
                importTreeHeaderBtn.addEventListener('click', () => {
                    importFile.value = '';
                    importJson.value = '';
                    importMessage.textContent = '';
                    importModal.style.display = 'flex';
                });
                
                // 关闭模态框
                closeImportModal.addEventListener('click', () => {
                    importModal.style.display = 'none';
                });
                
                // 导入决策树按钮点击
                importTreeBtn.addEventListener('click', async () => {
                    let treeData;
                    
                    if (importFile.files.length > 0) {
                        const file = importFile.files[0];
                        const text = await file.text();
                        
                        try {
                            treeData = JSON.parse(text);
                        } catch (error) {
                            importMessage.textContent = `文件解析失败: ${error.message}`;
                            importMessage.className = "error-message";
                            return;
                        }
                    } else if (importJson.value.trim()) {
                        try {
                            treeData = JSON.parse(importJson.value.trim());
                        } catch (error) {
                            importMessage.textContent = `JSON 解析失败: ${error.message}`;
                            importMessage.className = "error-message";
                            return;
                        }
                    } else {
                        importMessage.textContent = "请选择文件或输入 JSON";
                        importMessage.className = "error-message";
                        return;
                    }
                    
                    try {
                        const treeId = this.system.importTree(treeData);
                        
                        importMessage.textContent = "决策树导入成功!";
                        importMessage.className = "success-message";
                        
                        setTimeout(() => {
                            importModal.style.display = 'none';
                            this.renderTreeList();
                            this.loadTree(treeId);
                        }, 1000);
                    } catch (error) {
                        importMessage.textContent = `导入失败: ${error.message}`;
                        importMessage.className = "error-message";
                    }
                });
                
                            