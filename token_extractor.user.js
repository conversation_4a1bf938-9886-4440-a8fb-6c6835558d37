// ==UserScript==
// @name         VSCode Token Extractor
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  Extract token from VSCode URL
// <AUTHOR>
// @match        https://kilocode.ai/*
// @grant        GM_setClipboard
// @grant        GM_addStyle
// ==/UserScript==

(function() {
    'use strict';

    // Add styles for the button
    GM_addStyle(`
        #token-extractor-btn {
            position: fixed;
            bottom: 50%; /* 上移到页面中间位置 */
            right: 20px;
            z-index: 9999;
            width: 50px; /* 设置宽高相等，使其成为圆形 */
            height: 50px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 50%; /* 设置为圆形 */
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        #token-extractor-btn:hover {
            background-color: #45a049;
        }
        .token-message {
            position: fixed;
            bottom: 70px;
            right: 20px;
            background-color: #333;
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 9999;
            max-width: 300px;
            word-break: break-all;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        }
    `);

    // Create and add the button to the page
    const button = document.createElement('button');
    button.id = 'token-extractor-btn';
    button.textContent = '提取 Token';
    document.body.appendChild(button);

    // Function to extract and display the token
    function extractToken() {
        const linkElement = document.querySelector('a[href^="vscode://kilocode.kilo-code/kilocode?token="]');

        if (linkElement) {
            const href = linkElement.href;
            const token = href.split('token=')[1];

            // Copy to clipboard
            GM_setClipboard(token);

            // Show message
            const message = document.createElement('div');
            message.className = 'token-message';
            message.textContent = `Token 已复制到剪贴板: ${token}`;
            document.body.appendChild(message);

            // Remove message after 3 seconds
            setTimeout(() => {
                message.remove();
            }, 3000);
        } else {
            // Show error message
            const message = document.createElement('div');
            message.className = 'token-message';
            message.textContent = '未找到包含 token 的链接';
            document.body.appendChild(message);

            // Remove message after 3 seconds
            setTimeout(() => {
                message.remove();
            }, 3000);
        }
    }

    // Add click event listener to the button
    button.addEventListener('click', extractToken);
})();
