<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 决策树系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css">
    <style>
        .decision-tree {
            position: relative;
        }
        .decision-node {
            border-left: 2px solid #3b82f6;
            padding-left: 15px;
            margin-left: 15px;
            position: relative;
        }
        .decision-node::before {
            content: "";
            position: absolute;
            left: -2px;
            top: 0;
            width: 15px;
            height: 2px;
            background-color: #3b82f6;
        }
        .decision-node:first-child::before {
            display: none;
        }
        .probability-bar {
            height: 6px;
            background-color: #3b82f6;
            border-radius: 3px;
        }
        .follow-up-container {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .follow-up-container.active {
            max-height: 200px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(59, 130, 246, 0.3);
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .tooltip {
            position: relative;
            display: inline-block;
        }
        .tooltip .tooltip-text {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center text-gray-800 mb-8">AI 决策树系统</h1>
        
        <!-- 配置区域 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800">系统配置</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-gray-700 mb-2" for="api-endpoint">API 端点</label>
                    <input id="api-endpoint" type="text" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="https://api.openai.com/v1/chat/completions" value="https://api.openai.com/v1/chat/completions">
                </div>
                <div>
                    <label class="block text-gray-700 mb-2" for="api-key">API 密钥</label>
                    <input id="api-key" type="password" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="sk-...">
                </div>
                <div>
                    <label class="block text-gray-700 mb-2" for="model-name">模型名称</label>
                    <input id="model-name" type="text" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="gpt-4" value="gpt-4">
                </div>
                <div>
                    <label class="block text-gray-700 mb-2" for="prompt-template">提示词模板</label>
                    <textarea id="prompt-template" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3">针对问题："{question}"，请生成5个可能的决策选项，并为每个选项分配一个概率百分比（总和为100%）。按照以下格式回复：
1. [决策选项1] - [概率]%
2. [决策选项2] - [概率]%
...
请确保每个决策选项都是具体、可行的，并且概率分配合理反映每个选项的可能性。</textarea>
                </div>
            </div>
        </div>
        
        <!-- 问题输入区域 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800">输入问题</h2>
            <div class="mb-4">
                <input id="root-question" type="text" class="w-full px-4 py-3 text-lg border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="输入你的问题...">
            </div>
            <div class="flex flex-wrap gap-3">
                <button id="generate-btn" class="bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-700 transition duration-200">
                    生成决策
                </button>
                <button id="clear-btn" class="bg-red-600 text-white px-6 py-3 rounded hover:bg-red-700 transition duration-200">
                    清除数据
                </button>
                <button id="save-btn" class="bg-green-600 text-white px-6 py-3 rounded hover:bg-green-700 transition duration-200">
                    保存数据
                </button>
                <button id="load-btn" class="bg-yellow-600 text-white px-6 py-3 rounded hover:bg-yellow-700 transition duration-200">
                    加载数据
                </button>
            </div>
            <div id="loading-indicator" class="mt-4 text-gray-600 hidden">
                <i class="fas fa-spinner fa-spin mr-2"></i> 正在生成决策选项...
            </div>
        </div>

        <!-- 决策树显示区域 -->
        <div class="mt-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800">决策树</h2>
            <div id="decision-tree-container" class="decision-tree"></div>
        </div>

        <!-- 文件上传区域（隐藏） -->
        <input type="file" id="file-input" class="hidden" accept=".json">
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM 元素
            const apiEndpointInput = document.getElementById('api-endpoint');
            const apiKeyInput = document.getElementById('api-key');
            const modelNameInput = document.getElementById('model-name');
            const promptTemplateInput = document.getElementById('prompt-template');
            const rootQuestionInput = document.getElementById('root-question');
            const generateBtn = document.getElementById('generate-btn');
            const clearBtn = document.getElementById('clear-btn');
            const saveBtn = document.getElementById('save-btn');
            const loadBtn = document.getElementById('load-btn');
            const fileInput = document.getElementById('file-input');
            const decisionTreeContainer = document.getElementById('decision-tree-container');
            const loadingIndicator = document.getElementById('loading-indicator');

            // 应用状态
            const state = {
                decisionTree: null,
                currentNodeId: 0
            };

            // 决策树节点类
            class DecisionNode {
                constructor(id, question, parent = null, context = []) {
                    this.id = id;
                    this.question = question;
                    this.decisions = []; // [{text, probability, node}]
                    this.parent = parent;
                    this.aiResponse = ''; // 存储原始AI响应
                    this.context = context || []; // 存储上下文历史
                }

                addDecision(text, probability, childQuestion = null) {
                    const childId = state.currentNodeId++;
                    // 创建新的上下文，包含当前问题和决策
                    const newContext = [...this.context];
                    newContext.push({
                        role: 'user',
                        content: this.question
                    });
                    newContext.push({
                        role: 'assistant',
                        content: `决策: ${text} (概率: ${probability}%)`
                    });
                    
                    const childNode = childQuestion ? new DecisionNode(childId, childQuestion, this, newContext) : null;
                    this.decisions.push({
                        text: text,
                        probability: probability,
                        node: childNode
                    });
                    
                    // 按概率从高到低排序
                    this.decisions.sort((a, b) => b.probability - a.probability);
                    
                    return childNode;
                }

                toJSON() {
                    return {
                        id: this.id,
                        question: this.question,
                        aiResponse: this.aiResponse,
                        context: this.context,
                        decisions: this.decisions.map(d => ({
                            text: d.text,
                            probability: d.probability,
                            node: d.node ? d.node.toJSON() : null
                        }))
                    };
                }

                static fromJSON(json, parent = null) {
                    const node = new DecisionNode(json.id, json.question, parent, json.context || []);
                    node.aiResponse = json.aiResponse || '';
                    json.decisions.forEach(d => {
                        const decision = {
                            text: d.text,
                            probability: d.probability,
                            node: d.node ? DecisionNode.fromJSON(d.node, node) : null
                        };
                        node.decisions.push(decision);
                    });
                    // 确保按概率排序
                    node.decisions.sort((a, b) => b.probability - a.probability);
                    return node;
                }
            }

            // 初始化系统
            function initSystem() {
                state.decisionTree = null;
                state.currentNodeId = 0;
                decisionTreeContainer.innerHTML = '';

                // 从本地存储加载配置
                if (localStorage.getItem('decisionTreeConfig')) {
                    const config = JSON.parse(localStorage.getItem('decisionTreeConfig'));
                    apiEndpointInput.value = config.apiEndpoint || apiEndpointInput.value;
                    apiKeyInput.value = config.apiKey || '';
                    modelNameInput.value = config.modelName || modelNameInput.value;
                    promptTemplateInput.value = config.promptTemplate || promptTemplateInput.value;
                }
            }

            // 保存配置到本地存储
            function saveConfig() {
                const config = {
                    apiEndpoint: apiEndpointInput.value,
                    apiKey: apiKeyInput.value,
                    modelName: modelNameInput.value,
                    promptTemplate: promptTemplateInput.value
                };
                localStorage.setItem('decisionTreeConfig', JSON.stringify(config));
            }

            // 生成决策
            async function generateDecisions(question, parentNode = null) {
                if (!question) return;
                
                // 保存配置
                saveConfig();
                
                // 获取配置
                const apiEndpoint = apiEndpointInput.value;
                const apiKey = apiKeyInput.value;
                const modelName = modelNameInput.value;
                const promptTemplate = promptTemplateInput.value;
                
                if (!apiEndpoint || !apiKey || !modelName) {
                    alert('请填写API配置信息');
                    return;
                }
                
                loadingIndicator.classList.remove('hidden');
                
                try {
                    // 准备上下文和提示词
                    let messages = [];
                    
                    // 如果有父节点，添加上下文历史
                    if (parentNode && parentNode.context && parentNode.context.length > 0) {
                        messages = [...parentNode.context];
                    }
                    
                    // 添加当前问题
                    const prompt = promptTemplate.replace('{question}', question);
                    messages.push({
                        role: 'user',
                        content: prompt
                    });
                    
                    // 调用API
                    const response = await fetch(apiEndpoint, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${apiKey}`
                        },
                        body: JSON.stringify({
                            model: modelName,
                            messages: messages,
                            temperature: 0.7
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                    }

                    const data = await response.json();
                    const aiResponse = data.choices[0].message.content;

                    // 处理生成的决策
                    if (parentNode === null) {
                        // 创建根节点
                        state.decisionTree = new DecisionNode(state.currentNodeId++, question);
                        parentNode = state.decisionTree;
                    }

                    // 保存原始AI响应
                    parentNode.aiResponse = aiResponse;
                    
                    // 将AI响应添加到上下文
                    parentNode.context.push({
                        role: 'assistant',
                        content: aiResponse
                    });

                    // 解析决策和概率
                    const lines = aiResponse.split('\n');
                    const decisionRegex = /(\d+)\.\s+(.+?)\s+-\s+(\d+(?:\.\d+)?)%/;
                    
                    for (const line of lines) {
                        const match = line.match(decisionRegex);
                        if (match) {
                            const [, , decisionText, probability] = match;
                            parentNode.addDecision(decisionText, parseFloat(probability));
                        }
                    }

                    // 更新UI
                    renderDecisionTree();
                    loadingIndicator.classList.add('hidden');
                } catch (error) {
                    console.error('API请求错误:', error);
                    alert(`生成决策时出错: ${error.message}`);
                    loadingIndicator.classList.add('hidden');
                }
            }

            // 渲染决策树
            function renderDecisionTree() {
                if (!state.decisionTree) return;
                
                decisionTreeContainer.innerHTML = '';
                renderNode(state.decisionTree, decisionTreeContainer);
            }

            // 渲染单个节点及其决策
            function renderNode(node, container) {
                const nodeEl = document.createElement('div');
                nodeEl.className = 'decision-node mb-4';
                
                // 问题标题
                const questionEl = document.createElement('div');
                questionEl.className = 'font-semibold text-lg mb-2 text-gray-800';
                questionEl.textContent = node.question;
                nodeEl.appendChild(questionEl);
                
                // 决策列表
                const decisionsContainer = document.createElement('div');
                decisionsContainer.className = 'space-y-3 mt-3';
                
                node.decisions.forEach((decision, index) => {
                    const decisionEl = document.createElement('div');
                    decisionEl.className = 'bg-white p-3 rounded-lg shadow-sm border border-gray-200';
                    
                    // 决策标题和概率
                    const decisionHeader = document.createElement('div');
                    decisionHeader.className = 'flex justify-between items-center';
                    
                    const decisionTitle = document.createElement('div');
                    decisionTitle.className = 'font-medium text-gray-800';
                    decisionTitle.textContent = decision.text;
                    
                    const probabilityText = document.createElement('div');
                    probabilityText.className = 'text-blue-600 font-semibold';
                    probabilityText.textContent = `${decision.probability}%`;
                    
                    decisionHeader.appendChild(decisionTitle);
                    decisionHeader.appendChild(probabilityText);
                    decisionEl.appendChild(decisionHeader);
                    
                    // 概率条
                    const probabilityBarContainer = document.createElement('div');
                    probabilityBarContainer.className = 'w-full bg-gray-200 rounded-full h-1.5 mt-2';
                    
                    const probabilityBar = document.createElement('div');
                    probabilityBar.className = 'probability-bar';
                    probabilityBar.style.width = `${decision.probability}%`;
                    
                    probabilityBarContainer.appendChild(probabilityBar);
                    decisionEl.appendChild(probabilityBarContainer);
                    
                    // 如果没有子节点，添加后续问题输入
                    if (!decision.node) {
                        const followUpContainer = document.createElement('div');
                        followUpContainer.className = 'mt-3 pt-2 border-t border-gray-200 follow-up-container';
                        
                        const followUpInput = document.createElement('input');
                        followUpInput.type = 'text';
                        followUpInput.className = 'follow-up-question w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 mb-2';
                        followUpInput.placeholder = '输入后续问题...';
                        
                        const followUpBtn = document.createElement('button');
                        followUpBtn.className = 'ask-follow-up bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 transition duration-200';
                        followUpBtn.textContent = '提问';
                        followUpBtn.setAttribute('data-decision-index', index);
                        
                        followUpContainer.appendChild(followUpInput);
                        followUpContainer.appendChild(followUpBtn);
                        
                        // 添加展开/折叠按钮
                        const toggleBtn = document.createElement('button');
                        toggleBtn.className = 'text-blue-500 text-sm mt-2 hover:underline';
                        toggleBtn.textContent = '添加后续问题';
                        toggleBtn.onclick = function() {
                            followUpContainer.classList.toggle('active');
                            this.textContent = followUpContainer.classList.contains('active') ? '取消' : '添加后续问题';
                        };
                        
                        decisionEl.appendChild(toggleBtn);
                        decisionEl.appendChild(followUpContainer);
                    }
                    
                    // 如果已经有子节点，渲染它
                    if (decision.node) {
                        const childContainer = document.createElement('div');
                        childContainer.className = 'mt-4';
                        decisionEl.appendChild(childContainer);
                        renderNode(decision.node, childContainer);
                    } else {
                        // 为后续问题按钮添加事件监听器
                        decisionEl.querySelector('.ask-follow-up')?.addEventListener('click', function() {
                            const decisionIndex = parseInt(this.getAttribute('data-decision-index'));
                            const followUpQuestion = this.parentElement.querySelector('.follow-up-question').value.trim();
                            
                            if (followUpQuestion) {
                                // 创建子节点，继承上下文
                                const childNode = node.decisions[decisionIndex].node = new DecisionNode(
                                    state.currentNodeId++, 
                                    followUpQuestion, 
                                    node,
                                    [...node.context]
                                );
                                // 添加当前决策到上下文
                                childNode.context.push({
                                    role: 'user',
                                    content: `基于决策 "${node.decisions[decisionIndex].text}"，我有一个后续问题: ${followUpQuestion}`
                                });
                                generateDecisions(followUpQuestion, childNode);
                            } else {
                                alert('请输入后续问题');
                            }
                        });
                    }
                    
                    decisionsContainer.appendChild(decisionEl);
                });
                
                // 添加查看原始AI响应的按钮
                if (node.aiResponse) {
                    const responseBtn = document.createElement('div');
                    responseBtn.className = 'tooltip text-sm text-gray-500 mt-2 cursor-pointer';
                    responseBtn.innerHTML = '<i class="fas fa-info-circle mr-1"></i> 查看AI原始响应';
                    
                    const tooltipText = document.createElement('span');
                    tooltipText.className = 'tooltip-text';
                    tooltipText.textContent = node.aiResponse;
                    responseBtn.appendChild(tooltipText);
                    
                    nodeEl.appendChild(decisionsContainer);
                    nodeEl.appendChild(responseBtn);
                } else {
                    nodeEl.appendChild(decisionsContainer);
                }
                
                container.appendChild(nodeEl);
            }

            // 保存决策树到文件
            function saveDecisionTree() {
                if (!state.decisionTree) {
                    alert('没有决策树可以保存');
                    return;
                }
                
                const data = {
                    tree: state.decisionTree.toJSON(),
                    currentNodeId: state.currentNodeId,
                    config: {
                        apiEndpoint: apiEndpointInput.value,
                        apiKey: apiKeyInput.value,
                        modelName: modelNameInput.value,
                        promptTemplate: promptTemplateInput.value
                    }
                };
                
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `decision-tree-${new Date().toISOString().slice(0, 10)}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }

            // 从文件加载决策树
            function loadDecisionTree(file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const data = JSON.parse(e.target.result);
                        
                        if (data.tree) {
                            state.decisionTree = DecisionNode.fromJSON(data.tree);
                            state.currentNodeId = data.currentNodeId || 0;
                            
                            // 加载配置
                            if (data.config) {
                                apiEndpointInput.value = data.config.apiEndpoint || apiEndpointInput.value;
                                apiKeyInput.value = data.config.apiKey || '';
                                modelNameInput.value = data.config.modelName || modelNameInput.value;
                                promptTemplateInput.value = data.config.promptTemplate || promptTemplateInput.value;
                                saveConfig();
                            }
                            
                            // 更新UI
                            rootQuestionInput.value = state.decisionTree.question || '';
                            renderDecisionTree();
                        } else {
                            throw new Error('文件格式无效');
                        }
                    } catch (error) {
                        console.error('加载决策树错误:', error);
                        alert(`加载决策树时出错: ${error.message}`);
                    }
                };
                reader.readAsText(file);
            }

            // 事件监听器
            generateBtn.addEventListener('click', function() {
                const question = rootQuestionInput.value.trim();
                if (question) {
                    state.decisionTree = null;
                    decisionTreeContainer.innerHTML = '';
                    generateDecisions(question);
                } else {
                    alert('请输入一个问题');
                }
            });

            clearBtn.addEventListener('click', function() {
                if (confirm('确定要清除所有数据吗？')) {
                    initSystem();
                    rootQuestionInput.value = '';
                    decisionTreeContainer.innerHTML = '';
                }
            });

            saveBtn.addEventListener('click', saveDecisionTree);

            loadBtn.addEventListener('click', function() {
                fileInput.click();
            });

            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    loadDecisionTree(e.target.files[0]);
                    // 重置文件输入以便于再次选择同一文件
                    e.target.value = '';
                }
            });

            // 初始化系统
            initSystem();
        });
    </script>
</body>
</html>
