#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Token统计工具
快速统计销售培训系统文件夹的token数量
"""

import os
import sys
from pathlib import Path

def count_chinese_tokens(text):
    """
    简单的中文token估算方法
    中文字符按1个token计算，英文单词按平均1.3个token计算
    """
    chinese_chars = 0
    english_words = 0
    
    for char in text:
        if '\u4e00' <= char <= '\u9fff':  # 中文字符范围
            chinese_chars += 1
        elif char.isalpha():
            english_words += 1
    
    # 估算英文单词数（按空格分割）
    english_word_count = len([word for word in text.split() if any(c.isalpha() for c in word)])
    
    # 中文字符 * 1 + 英文单词 * 1.3
    estimated_tokens = chinese_chars + int(english_word_count * 1.3)
    return estimated_tokens

def read_file_safe(file_path):
    """安全读取文件，尝试多种编码"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
    
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except:
            continue
    
    print(f"警告: 无法读取文件 {file_path}")
    return ""

def main():
    folder_path = Path("销售培训系统")
    
    if not folder_path.exists():
        print(f"错误: 文件夹 '{folder_path}' 不存在")
        return
    
    print("销售培训系统Token统计")
    print("=" * 50)
    
    total_tokens = 0
    total_chars = 0
    file_count = 0
    file_stats = []
    
    # 遍历所有txt文件
    for file_path in folder_path.glob("*.txt"):
        print(f"处理: {file_path.name}")
        
        content = read_file_safe(file_path)
        if content:
            char_count = len(content)
            token_count = count_chinese_tokens(content)
            
            file_stats.append({
                'name': file_path.name,
                'tokens': token_count,
                'chars': char_count
            })
            
            total_tokens += token_count
            total_chars += char_count
            file_count += 1
            
            print(f"  字符数: {char_count:,}")
            print(f"  估算Token: {token_count:,}")
            print()
    
    # 打印总结
    print("=" * 50)
    print("统计总结:")
    print(f"文件总数: {file_count}")
    print(f"总字符数: {total_chars:,}")
    print(f"估算总Token数: {total_tokens:,}")
    print(f"平均每文件Token数: {total_tokens // max(file_count, 1):,}")
    print()
    
    # 显示Token数最多的前10个文件
    if file_stats:
        print("Token数量最多的文件:")
        print("-" * 50)
        sorted_files = sorted(file_stats, key=lambda x: x['tokens'], reverse=True)
        
        for i, file_info in enumerate(sorted_files[:10], 1):
            print(f"{i:2d}. {file_info['name']}")
            print(f"    Token: {file_info['tokens']:,}, 字符: {file_info['chars']:,}")
    
    # 保存结果到文件
    output_file = "token_统计结果.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("销售培训系统Token统计结果\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"文件总数: {file_count}\n")
            f.write(f"总字符数: {total_chars:,}\n")
            f.write(f"估算总Token数: {total_tokens:,}\n")
            f.write(f"平均每文件Token数: {total_tokens // max(file_count, 1):,}\n\n")
            
            f.write("详细文件统计:\n")
            f.write("-" * 50 + "\n")
            
            for file_info in sorted(file_stats, key=lambda x: x['tokens'], reverse=True):
                f.write(f"文件: {file_info['name']}\n")
                f.write(f"  字符数: {file_info['chars']:,}\n")
                f.write(f"  估算Token: {file_info['tokens']:,}\n\n")
        
        print(f"\n结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存结果时出错: {e}")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
