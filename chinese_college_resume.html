<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大学申请简历</title>
    <style>
        /* 重置和基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Microsoft YaHei', 'SimHei', 'STHeiti', sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }
        
        .resume-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 40px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        /* 头部样式 */
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 20px;
        }
        
        .name {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .contact-info {
            font-size: 16px;
            color: #7f8c8d;
        }
        
        /* 部分样式 */
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 22px;
            font-weight: bold;
            color: #2c3e50;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
            margin-bottom: 15px;
        }
        
        /* 条目样式 */
        .entry {
            margin-bottom: 15px;
        }
        
        .entry-title {
            font-weight: bold;
            font-size: 18px;
            color: #3498db;
        }
        
        .entry-subtitle {
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .entry-date {
            color: #7f8c8d;
            font-style: italic;
        }
        
        .entry-description {
            margin-top: 5px;
        }
        
        /* 技能部分 */
        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .skill {
            background-color: #e8f4fc;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 14px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .resume-container {
                padding: 20px;
            }
            
            .name {
                font-size: 28px;
            }
            
            .section-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <!-- 头部信息 -->
        <div class="header">
            <div class="name">王明</div>
            <div class="contact-info">
                <p>北京市海淀区中关村南大街5号，100081</p>
                <p>电话: 138-1234-5678 | 邮箱: <EMAIL></p>
                <p>微信: wangming123 | 个人网站: wangming.example.com</p>
            </div>
        </div>
        
        <!-- 教育经历 -->
        <div class="section">
            <div class="section-title">教育经历</div>
            <div class="entry">
                <div class="entry-title">北京市第四中学</div>
                <div class="entry-subtitle">
                    <span>高中毕业</span>
                    <span class="entry-date">2021年6月</span>
                </div>
                <div class="entry-description">
                    <p>高中成绩优异，获得校级三好学生称号</p>
                    <p>高考成绩: 总分635分（满分750分）</p>
                    <p>主要科目: 语文128分，数学140分，英语138分，理综229分</p>
                </div>
            </div>
            <div class="entry">
                <div class="entry-title">北京市第十二中学</div>
                <div class="entry-subtitle">
                    <span>初中毕业</span>
                    <span class="entry-date">2018年6月</span>
                </div>
                <div class="entry-description">
                    <p>初中三年保持年级前10%的成绩</p>
                    <p>中考成绩: 总分535分（满分580分）</p>
                    <p>活动参与: 学生会成员，校数学竞赛一等奖，校英语演讲比赛二等奖</p>
                </div>
            </div>
        </div>
        
        <!-- 技能特长 -->
        <div class="section">
            <div class="section-title">技能特长</div>
            <div class="skills-list">
                <div class="skill">英语（CET-6）</div>
                <div class="skill">计算机（二级证书）</div>
                <div class="skill">Python编程</div>
                <div class="skill">数据分析</div>
                <div class="skill">Microsoft Office</div>
                <div class="skill">团队协作</div>
                <div class="skill">公众演讲</div>
                <div class="skill">时间管理</div>
                <div class="skill">批判性思维</div>
            </div>
        </div>
        
        <!-- 实践经历 -->
        <div class="section">
            <div class="section-title">实践经历</div>
            <div class="entry">
                <div class="entry-title">科技创新实践活动</div>
                <div class="entry-subtitle">
                    <span>北京青少年科技中心</span>
                    <span class="entry-date">2020年7月 - 2020年8月</span>
                </div>
                <div class="entry-description">
                    <ul>
                        <li>参与"智能家居"项目开发，负责数据收集和分析</li>
                        <li>使用Python编程实现简单的数据可视化</li>
                        <li>与团队成员合作完成项目展示和汇报</li>
                    </ul>
                </div>
            </div>
            <div class="entry">
                <div class="entry-title">社区志愿服务</div>
                <div class="entry-subtitle">
                    <span>海淀区青年志愿者协会</span>
                    <span class="entry-date">2019年9月 - 2021年5月</span>
                </div>
                <div class="entry-description">
                    <ul>
                        <li>参与社区环保宣传活动，向居民普及垃圾分类知识</li>
                        <li>为社区老年人提供数字技能培训，教授基本的智能手机使用方法</li>
                        <li>累计志愿服务时间超过100小时</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 项目经历 -->
        <div class="section">
            <div class="section-title">项目经历</div>
            <div class="entry">
                <div class="entry-title">校园环保数据分析</div>
                <div class="entry-subtitle">
                    <span>高中科学课程项目</span>
                    <span class="entry-date">2020年11月</span>
                </div>
                <div class="entry-description">
                    <p>收集并分析学校垃圾分类数据，使用Excel制作数据图表，提出改进校园环保的建议方案，获得老师高度评价。</p>
                </div>
            </div>
            <div class="entry">
                <div class="entry-title">英语演讲比赛</div>
                <div class="entry-subtitle">
                    <span>校级英语活动</span>
                    <span class="entry-date">2019年10月</span>
                </div>
                <div class="entry-description">
                    <p>以"科技与未来"为主题，准备并进行了一场5分钟的英语演讲，获得校级比赛三等奖。</p>
                </div>
            </div>
        </div>
        
        <!-- 课外活动 -->
        <div class="section">
            <div class="section-title">课外活动与领导力</div>
            <div class="entry">
                <div class="entry-title">学生会成员</div>
                <div class="entry-subtitle">
                    <span>学习部副部长</span>
                    <span class="entry-date">2019年9月 - 2021年6月</span>
                </div>
                <div class="entry-description">
                    <p>协助组织校内学术竞赛和学习经验交流会，提高了组织能力和团队协作能力。</p>
                </div>
            </div>
            <div class="entry">
                <div class="entry-title">科技兴趣小组</div>
                <div class="entry-subtitle">
                    <span>核心成员</span>
                    <span class="entry-date">2018年10月 - 2021年5月</span>
                </div>
                <div class="entry-description">
                    <p>参与科技创新活动，学习编程和电子技术基础，培养了解决问题的能力和创新思维。</p>
                </div>
            </div>
        </div>
        
        <!-- 获奖情况 -->
        <div class="section">
            <div class="section-title">获奖情况</div>
            <div class="entry">
                <div class="entry-title">三好学生</div>
                <div class="entry-subtitle">
                    <span>北京市第四中学</span>
                    <span class="entry-date">2020年度</span>
                </div>
            </div>
            <div class="entry">
                <div class="entry-title">数学竞赛一等奖</div>
                <div class="entry-subtitle">
                    <span>校级数学竞赛</span>
                    <span class="entry-date">2019年11月</span>
                </div>
                <div class="entry-description">
                    <p>在校级数学竞赛中获得一等奖，展示了较强的数学分析能力和解题能力。</p>
                </div>
            </div>
        </div>
        
        <!-- 个人陈述 -->
        <div class="section">
            <div class="section-title">个人陈述</div>
            <div class="entry-description">
                <p>我是一名热爱学习、积极进取的学生，对计算机科学和数据分析有浓厚的兴趣。在高中阶段，我不仅注重学科知识的学习，还积极参与各类实践活动和社会服务，全面发展自己的能力。我希望能够在大学期间深入学习计算机科学和人工智能相关知识，为未来在科技领域的发展打下坚实基础。</p>
            </div>
        </div>
    </div>
</body>
</html>
