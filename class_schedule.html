<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Class Schedule</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #8C1D40;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .logo-container {
            display: flex;
            justify-content: center;
            margin-bottom: 15px;
        }
        .asu-logo {
            height: 120px;
            margin: 0 auto;
        }
        .university-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #8C1D40;
            /* 显示学校全称 */
            display: block;
        }
        .document-title {
            font-size: 22px;
            margin-top: 10px;
            margin-bottom: 5px;
        }
        .semester {
            font-size: 18px;
            color: #555;
        }
        .student-info {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .info-row {
            display: flex;
            margin-bottom: 8px;
        }
        .info-label {
            flex: 0 0 120px;
            font-weight: bold;
        }
        .info-value {
            flex: 1;
            font-weight: 500;
        }
        .highlight {
            background-color: #FFF9E8;
            padding: 3px 5px;
            border-radius: 3px;
            border-left: 3px solid #8C1D40;
        }
        .schedule {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .schedule th {
            background-color: #8C1D40;
            color: white;
            padding: 10px;
            text-align: left;
        }
        .schedule td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }
        .schedule tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 40px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
            font-size: 14px;
            color: #666;
        }
        .date-container {
            margin-bottom: 20px;
            text-align: center;
            padding: 10px;
            border: 2px dashed #8C1D40;
            background-color: #FFF9E8;
            border-radius: 5px;
        }
        .date-label {
            font-weight: bold;
            font-size: 16px;
            color: #333;
        }
        .date-value {
            font-weight: bold;
            font-size: 18px;
            color: #8C1D40;
            margin-top: 5px;
        }
        .date-generated {
            margin-bottom: 10px;
            font-weight: bold;
            color: #333;
            background-color: #FFF9E8;
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .official-seal {
            float: right;
            width: 100px;
            height: 100px;
            background-image: url('data:image/png;base64,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');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            margin-left: 20px;
        }
        .contact-info {
            margin-top: 40px;
            font-style: italic;
        }
        @media print {
            body {
                background-color: white;
                padding: 0;
            }
            .container {
                box-shadow: none;
                max-width: 100%;
            }
            .print-button {
                display: none;
            }
            .highlight {
                background-color: transparent;
                border-left: none;
                padding: 0;
            }
            .date-generated {
                background-color: transparent;
            }
            .date-container {
                border: 1px solid #8C1D40;
                background-color: transparent;
            }
        }
        .print-button {
            background-color: #8C1D40;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            margin-top: 20px;
            font-size: 16px;
        }
        .print-button:hover {
            background-color: #6d1731;
        }
        .term-info {
            font-size: 14px;
            margin-top: 10px;
            color: #555;
        }
        .validation-mark {
            font-weight: bold;
            color: #006633;
            display: inline-block;
            margin-left: 5px;
        }
        .expiration-date {
            color: #8C1D40;
            font-weight: bold;
        }
        .asu-logo-image {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                <img src="https://www.asu.edu/asuthemes/5.0/assets/arizona-state-university-logo.png" alt="Arizona State University Logo" class="asu-logo" style="height: 80px;">
            </div>
            <div class="university-name">ARIZONA STATE UNIVERSITY</div>
            <div class="document-title">Student Class Schedule</div>
            <div class="semester">Spring Semester 2025</div>
            <div class="term-info">Term: 2251 (January 6, 2025 - May 2, 2025)</div>
        </div>
        <div class="date-container">
            <div class="date-label">CURRENT ISSUE DATE:</div>
            <div class="date-value" id="current-date">April 20, 2024</div>
            <div>Academic Year 2024-2025</div>
            <div style="margin-top: 5px; font-size: 14px;"><span class="expiration-date"></span></div>
        </div>
        <div class="student-info">
            <div class="info-row">
                <div class="info-label">Student Name:</div>
                <div class="info-value" style="font-size: 18px; font-weight: bold;">Tom Leo</div>
            </div>
            <div class="info-row">
                <div class="info-label">ASU ID:</div>
                <div class="info-value">mwils179</div>
            </div>
            <div class="info-row">
                <div class="info-label">College:</div>
                <div class="info-value">Ira A. Fulton Schools of Engineering</div>
            </div>
            <div class="info-row">
                <div class="info-label">Major:</div>
                <div class="info-value">Computer Science, BS</div>
            </div>
            <div class="info-row">
                <div class="info-label">Class Standing:</div>
                <div class="info-value">Junior</div>
            </div>
            <div class="info-row">
                <div class="info-label">Campus:</div>
                <div class="info-value">Tempe</div>
            </div>
        </div>
        <table class="schedule">
            <thead>
                <tr>
                    <th>Class #</th>
                    <th>Course</th>
                    <th>Units</th>
                    <th>Days/Time</th>
                    <th>Location</th>
                    <th>Instructor</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>23456</td>
                    <td>CSE 355: Theory of Computation</td>
                    <td>3</td>
                    <td>MW 10:30AM-11:45AM</td>
                    <td>COOR 170</td>
                    <td>Williams, S.</td>
                </tr>
                <tr>
                    <td>24786</td>
                    <td>CSE 360: Introduction to Software Engineering</td>
                    <td>3</td>
                    <td>TTh 1:30PM-2:45PM</td>
                    <td>BYENG 210</td>
                    <td>Chen, L.</td>
                </tr>
                <tr>
                    <td>25123</td>
                    <td>CSE 420: Computer Architecture I</td>
                    <td>3</td>
                    <td>WF 3:00PM-4:15PM</td>
                    <td>CAVC 351</td>
                    <td>Brown, K.</td>
                </tr>
                <tr>
                    <td>21435</td>
                    <td>CSE 445: Distributed Software Development</td>
                    <td>3</td>
                    <td>MW 1:30PM-2:45PM</td>
                    <td>PSH 151</td>
                    <td>Patel, R.</td>
                </tr>
                <tr>
                    <td>20452</td>
                    <td>CSE 434: Computer Networks</td>
                    <td>3</td>
                    <td>TTh 9:00AM-10:15AM</td>
                    <td>PSF 173</td>
                    <td>Kim, J.</td>
                </tr>
                <tr>
                    <td>20985</td>
                    <td>SER 421: Web-Based Applications</td>
                    <td>3</td>
                    <td>F 9:00AM-11:45AM</td>
                    <td>PSH 365</td>
                    <td>Thompson, D.</td>
                </tr>
            </tbody>
        </table>
        <div style="margin-top:20px;">
            <div style="font-weight:bold;">Total Units: 18</div>
            <div style="margin-top:5px; font-size:13px;">*Schedule is subject to change. Check MyASU for the most current information.</div>
        </div>
        <div class="footer">
            <div class="official-seal"></div>
            <div class="date-generated">ARIZONA STATE UNIVERSITY OFFICIAL DOCUMENT</div>
            <div class="document-id">Document ID: SCH-mwils179-2251-001</div>
            <div class="contact-info">
                Arizona State University<br>
                University Registrar Services<br>
                PO Box 870312, Tempe, AZ 85287-0312<br>
                Phone: (*************<br>
                Email: <EMAIL>
            </div>
        </div>
    </div>
    <script>
        // Set current date and handle semester information
        document.addEventListener('DOMContentLoaded', function() {
            // Use a fixed date within the current academic year
            document.getElementById('current-date').textContent = 'April 20, 2024';
            // Set fixed expiration date
            document.querySelector('.expiration-date').textContent = 'Valid until: July 19, 2024';
            // Update academic year based on term
            document.querySelector('.date-container div:nth-child(3)').textContent = `Academic Year 2024-2025`;
        });
    </script>
</body>
</html>