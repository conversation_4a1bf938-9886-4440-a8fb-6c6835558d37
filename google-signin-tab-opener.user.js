// ==UserScript==
// @name         Google Sign-in Tab Opener
// @namespace    http://tampermonkey.net/
// @version      1.1
// @description  Adds a button to Google sign-in page to open 9 additional tabs of the same page
// <AUTHOR>
// @match        https://accounts.google.com/v3/signin/identifier*
// @match        https://accounts.google.com/v3/signin/identifier?hl=en&continue=https%3A%2F%2Fwww.google.com.hk%2F%3Fpli%3D1&ec=GAlAmgQ&authuser=0&ddm=1&flowName=GlifWebSignIn&flowEntry=AddSession
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // Function to create and add the button
    function addButton() {
        // Create button element
        const button = document.createElement('button');
        button.textContent = '打开9个标签页';
        button.id = 'openTabsButton';

        // Style the button
        button.style.position = 'fixed';
        button.style.right = '20px';
        button.style.top = '20px';
        button.style.zIndex = '9999';
        button.style.padding = '10px 15px';
        button.style.backgroundColor = '#4285f4';
        button.style.color = 'white';
        button.style.border = 'none';
        button.style.borderRadius = '4px';
        button.style.cursor = 'pointer';
        button.style.fontWeight = 'bold';

        // Add hover effect
        button.addEventListener('mouseover', function() {
            this.style.backgroundColor = '#3367d6';
        });

        button.addEventListener('mouseout', function() {
            this.style.backgroundColor = '#4285f4';
        });

        // Add click event listener
        button.addEventListener('click', openTabs);

        // Add button to the page
        document.body.appendChild(button);
    }

    // Function to open 9 additional tabs
    function openTabs() {
        const currentUrl = window.location.href;

        // Open 9 new tabs with the same URL
        for (let i = 0; i < 9; i++) {
            window.open(currentUrl, '_blank');
        }
    }

    // Add the button when the page is fully loaded
    window.addEventListener('load', function() {
        // Small delay to ensure the page is fully rendered
        setTimeout(addButton, 1000);
    });

    // Also try to add the button immediately in case the page is already loaded
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        setTimeout(addButton, 500);
    }
})();
