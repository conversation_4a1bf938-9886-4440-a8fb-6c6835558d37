<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Unofficial Transcript - Arizona State University</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&family=Open+Sans:wght@400;700&display=swap');

    body {
      font-family: 'Open Sans', sans-serif;
      font-size: 12px;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      min-height: 100vh;
      background-color: #f0f0f0;
    }
    .page {
      width: 210mm;
      min-height: 297mm;
      padding: 20mm;
      margin: 10mm auto;
      background-color: white;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      border-bottom: 2px solid #8C1D40;
      padding-bottom: 15px;
    }
    .logo {
      font-size: 20px;
      font-weight: bold;
      color: #8C1D40;
      font-family: 'Times New Roman', serif;
      display: flex;
      align-items: center;
    }
    .logo img {
      height: 60px;
      margin-right: 15px;
    }
    .logo-text {
      display: flex;
      flex-direction: column;
    }
    .school-name {
      font-size: 22px;
    }
    .school-address {
      font-size: 12px;
      font-weight: normal;
    }
    h1 {
      font-family: 'Roboto', sans-serif;
      font-size: 18px;
      margin-bottom: 10px;
      color: #8C1D40;
      border-bottom: 1px solid #ddd;
      padding-bottom: 5px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
      font-weight: bold;
    }
    .student-info, .gpa-info {
      margin-bottom: 20px;
    }
    .student-info div, .gpa-info div {
      margin-bottom: 5px;
    }
    .transcript-title {
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      margin: 15px 0;
    }
    .section {
      margin-bottom: 25px;
    }
    .academic-summary {
      display: flex;
      justify-content: space-between;
    }
    .summary-box {
      border: 1px solid #ddd;
      padding: 10px;
      width: 48%;
    }
    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 11px;
      color: #666;
      padding-top: 10px;
    }
  </style>
</head>
<body>
<div class="page">
  <div class="header">
    <div class="logo">
      <div class="logo-text">
        <div class="school-name">Arizona State University</div>
        <div class="school-address">University Drive and Mill Avenue, Tempe, AZ 85287</div>
        <div class="school-address">Phone: (************* • Fax: (*************</div>
      </div>
    </div>
    <div>
      <div>Print Date: 04/15/2025</div>
      <div><strong>Unofficial Transcript</strong></div>
      <div>CEEB Code: 001081</div>
    </div>
  </div>

  <div class="transcript-title">STUDENT ACADEMIC RECORD</div>

  <div class="student-info">
    <div><strong>Student Name:</strong> Bill Green</div>
    <div><strong>Student ID:</strong> A20581937</div>
    <div><strong>Date of Birth:</strong> 02/02/2005</div>
    <div><strong>Enrollment Date:</strong> August 2024</div>
    <div><strong>Address:</strong> 1225 E University Dr, Tempe, AZ 85281</div>
    <div><strong>Major:</strong> Bachelor of Science - Computer Science</div>
  </div>

  <div class="academic-summary">
    <div class="summary-box">
      <div><strong>Cumulative GPA:</strong> 3.4</div>
      <div><strong>Credits Attempted:</strong> 30.0</div>
      <div><strong>Credits Earned:</strong> 30.0</div>
    </div>
    <div class="summary-box">
      <div><strong>Academic Standing:</strong> Good Standing</div>
      <div><strong>Degree Progress:</strong> 25% (30/120 credits)</div>
      <div><strong>Academic Achievement:</strong> Dean's List</div>
    </div>
  </div>

  <h1>FRESHMAN YEAR - FALL SEMESTER (2024)</h1>
  <table>
    <tr>
      <th>Course ID</th>
      <th>Course Title</th>
      <th>Grade</th>
      <th>Credits</th>
    </tr>
    <tr>
      <td>ENG 101</td>
      <td>First-Year Composition</td>
      <td>A-</td>
      <td>3.0</td>
    </tr>
    <tr>
      <td>CSE 110</td>
      <td>Principles of Programming</td>
      <td>B+</td>
      <td>3.0</td>
    </tr>
    <tr>
      <td>MAT 265</td>
      <td>Calculus for Engineers I</td>
      <td>B</td>
      <td>3.0</td>
    </tr>
    <tr>
      <td>PHY 121</td>
      <td>University Physics I: Mechanics</td>
      <td>B+</td>
      <td>3.0</td>
    </tr>
    <tr>
      <td>PHY 122</td>
      <td>University Physics Laboratory I</td>
      <td>A</td>
      <td>1.0</td>
    </tr>
    <tr>
      <td>ASU 101</td>
      <td>The ASU Experience</td>
      <td>A</td>
      <td>1.0</td>
    </tr>
    <tr>
      <th colspan="4">Semester GPA: 3.4 • Credits: 14.0</th>
    </tr>
  </table>

  <h1>FRESHMAN YEAR - SPRING SEMESTER (2025)</h1>
  <table>
    <tr>
      <th>Course ID</th>
      <th>Course Title</th>
      <th>Grade</th>
      <th>Credits</th>
    </tr>
    <tr>
      <td>ENG 102</td>
      <td>First-Year Composition</td>
      <td>A</td>
      <td>3.0</td>
    </tr>
    <tr>
      <td>CSE 205</td>
      <td>Object-Oriented Programming and Data Structures</td>
      <td>B+</td>
      <td>3.0</td>
    </tr>
    <tr>
      <td>MAT 266</td>
      <td>Calculus for Engineers II</td>
      <td>B</td>
      <td>3.0</td>
    </tr>
    <tr>
      <td>PHY 131</td>
      <td>University Physics II: Electricity and Magnetism</td>
      <td>B+</td>
      <td>3.0</td>
    </tr>
    <tr>
      <td>PHY 132</td>
      <td>University Physics Laboratory II</td>
      <td>A</td>
      <td>1.0</td>
    </tr>
    <tr>
      <td>COM 225</td>
      <td>Public Speaking</td>
      <td>A-</td>
      <td>3.0</td>
    </tr>
    <tr>
      <th colspan="4">Semester GPA: 3.5 • Credits Earned: 16.0</th>
    </tr>
  </table>

  <div class="section">
    <h1>ACADEMIC NOTES</h1>
    <table>
      <tr>
        <th>Date</th>
        <th>Note</th>
      </tr>
      <tr>
        <td>12/18/2024</td>
        <td>Dean's List - Fall Semester</td>
      </tr>
      <tr>
        <td>08/20/2024</td>
        <td>New Student Orientation Completed</td>
      </tr>
    </table>
  </div>

  <div class="footer">
    <p>This transcript is unofficial and for reference only.</p>
  </div>
</div>
</body>
</html>
