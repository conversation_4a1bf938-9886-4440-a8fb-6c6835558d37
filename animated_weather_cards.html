<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animated Weather Cards</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background-color: #121212;
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 2rem;
        }

        h1 {
            margin-bottom: 2rem;
            text-align: center;
        }

        .container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 2rem;
            max-width: 1200px;
        }

        .weather-card {
            width: 250px;
            height: 350px;
            background-color: #1e1e1e;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
        }

        .weather-card:hover {
            transform: translateY(-10px);
        }

        .card-header {
            padding: 1.5rem;
            text-align: center;
            background-color: rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 2;
        }

        .card-title {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .card-temp {
            font-size: 2.5rem;
            font-weight: bold;
        }

        .card-content {
            height: calc(100% - 100px);
            position: relative;
            overflow: hidden;
        }

        /* Wind Animation */
        .wind-card .card-content {
            background: linear-gradient(to bottom, #2c3e50, #34495e);
        }

        .cloud {
            position: absolute;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
        }

        .cloud-1 {
            width: 60px;
            height: 20px;
            top: 30%;
            left: -60px;
            animation: moveCloud 8s linear infinite;
        }

        .cloud-2 {
            width: 80px;
            height: 25px;
            top: 50%;
            left: -80px;
            animation: moveCloud 12s linear infinite;
            animation-delay: 2s;
        }

        .cloud-3 {
            width: 40px;
            height: 15px;
            top: 70%;
            left: -40px;
            animation: moveCloud 6s linear infinite;
            animation-delay: 4s;
        }

        .wind-line {
            position: absolute;
            height: 2px;
            background-color: rgba(255, 255, 255, 0.4);
            animation: windLine 3s linear infinite;
        }

        @keyframes moveCloud {
            0% {
                left: -80px;
            }
            100% {
                left: 100%;
            }
        }

        @keyframes windLine {
            0% {
                width: 0;
                left: 0;
                opacity: 0.7;
            }
            100% {
                width: 100%;
                left: 0;
                opacity: 0;
            }
        }

        /* Rain Animation */
        .rain-card .card-content {
            background: linear-gradient(to bottom, #2c3e50, #3498db);
        }

        .raindrop {
            position: absolute;
            width: 2px;
            background-color: rgba(255, 255, 255, 0.7);
            animation: rain linear infinite;
        }

        @keyframes rain {
            0% {
                top: -20px;
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            100% {
                top: 100%;
                opacity: 0.3;
            }
        }

        /* Sun Animation */
        .sun-card .card-content {
            background: linear-gradient(to bottom, #f39c12, #e67e22);
            overflow: hidden;
        }

        .sun {
            position: absolute;
            width: 80px;
            height: 80px;
            background-color: #f1c40f;
            border-radius: 50%;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 0 0 40px #f39c12;
            animation: pulse 3s ease-in-out infinite alternate;
        }

        .sun-ray {
            position: absolute;
            background-color: rgba(255, 255, 255, 0.4);
            transform-origin: bottom center;
            animation: sunRay 3s ease-in-out infinite alternate;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 40px #f39c12;
            }
            100% {
                box-shadow: 0 0 70px #f39c12;
            }
        }

        @keyframes sunRay {
            0% {
                opacity: 0.3;
                height: 60px;
            }
            100% {
                opacity: 0.7;
                height: 100px;
            }
        }

        /* Snow Animation */
        .snow-card .card-content {
            background: linear-gradient(to bottom, #34495e, #7f8c8d);
        }

        .snowflake {
            position: absolute;
            color: white;
            font-size: 20px;
            animation: snowfall linear infinite;
        }

        @keyframes snowfall {
            0% {
                top: -20px;
                opacity: 0;
                transform: translateX(0) rotate(0deg);
            }
            10% {
                opacity: 1;
            }
            100% {
                top: 100%;
                opacity: 0.6;
                transform: translateX(20px) rotate(360deg);
            }
        }

        /* Controls */
        .controls {
            margin-top: 2rem;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-btn {
            padding: 0.8rem 1.5rem;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s ease;
        }

        .control-btn:hover {
            background-color: #2980b9;
        }

        .control-btn.active {
            background-color: #e74c3c;
        }
    </style>
</head>
<body>
    <h1>Animated Weather Cards</h1>
    
    <div class="container">
        <!-- Wind Card -->
        <div class="weather-card wind-card">
            <div class="card-header">
                <div class="card-title">Windy</div>
                <div class="card-temp">18°C</div>
            </div>
            <div class="card-content">
                <div class="cloud cloud-1"></div>
                <div class="cloud cloud-2"></div>
                <div class="cloud cloud-3"></div>
                <!-- Wind lines will be added by JavaScript -->
            </div>
        </div>

        <!-- Rain Card -->
        <div class="weather-card rain-card">
            <div class="card-header">
                <div class="card-title">Rainy</div>
                <div class="card-temp">14°C</div>
            </div>
            <div class="card-content">
                <!-- Raindrops will be added by JavaScript -->
            </div>
        </div>

        <!-- Sun Card -->
        <div class="weather-card sun-card">
            <div class="card-header">
                <div class="card-title">Sunny</div>
                <div class="card-temp">28°C</div>
            </div>
            <div class="card-content">
                <div class="sun"></div>
                <!-- Sun rays will be added by JavaScript -->
            </div>
        </div>

        <!-- Snow Card -->
        <div class="weather-card snow-card">
            <div class="card-header">
                <div class="card-title">Snowy</div>
                <div class="card-temp">-2°C</div>
            </div>
            <div class="card-content">
                <!-- Snowflakes will be added by JavaScript -->
            </div>
        </div>
    </div>

    <div class="controls">
        <button class="control-btn" data-weather="wind">Enhance Wind</button>
        <button class="control-btn" data-weather="rain">Enhance Rain</button>
        <button class="control-btn" data-weather="sun">Enhance Sun</button>
        <button class="control-btn" data-weather="snow">Enhance Snow</button>
        <button class="control-btn" data-weather="all">Show All</button>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get all weather cards
            const windCard = document.querySelector('.wind-card .card-content');
            const rainCard = document.querySelector('.rain-card .card-content');
            const sunCard = document.querySelector('.sun-card .card-content');
            const snowCard = document.querySelector('.snow-card .card-content');
            
            // Initialize all animations
            initWindAnimation(windCard);
            initRainAnimation(rainCard);
            initSunAnimation(sunCard);
            initSnowAnimation(snowCard);

            // Control buttons functionality
            const buttons = document.querySelectorAll('.control-btn');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    const weather = this.getAttribute('data-weather');
                    
                    // Remove active class from all buttons
                    buttons.forEach(btn => btn.classList.remove('active'));
                    
                    // Add active class to clicked button
                    this.classList.add('active');
                    
                    // Enhance the selected weather animation
                    if (weather === 'wind') {
                        enhanceWindAnimation(windCard);
                        resetRainAnimation(rainCard);
                        resetSunAnimation(sunCard);
                        resetSnowAnimation(snowCard);
                    } else if (weather === 'rain') {
                        resetWindAnimation(windCard);
                        enhanceRainAnimation(rainCard);
                        resetSunAnimation(sunCard);
                        resetSnowAnimation(snowCard);
                    } else if (weather === 'sun') {
                        resetWindAnimation(windCard);
                        resetRainAnimation(rainCard);
                        enhanceSunAnimation(sunCard);
                        resetSnowAnimation(snowCard);
                    } else if (weather === 'snow') {
                        resetWindAnimation(windCard);
                        resetRainAnimation(rainCard);
                        resetSunAnimation(sunCard);
                        enhanceSnowAnimation(snowCard);
                    } else if (weather === 'all') {
                        initWindAnimation(windCard);
                        initRainAnimation(rainCard);
                        initSunAnimation(sunCard);
                        initSnowAnimation(snowCard);
                    }
                });
            });

            // Wind Animation Functions
            function initWindAnimation(card) {
                // Clear existing elements
                clearWindElements(card);
                
                // Add wind lines
                for (let i = 0; i < 5; i++) {
                    const line = document.createElement('div');
                    line.className = 'wind-line';
                    line.style.top = `${20 + i * 30}px`;
                    line.style.animationDelay = `${i * 0.5}s`;
                    card.appendChild(line);
                }
            }

            function enhanceWindAnimation(card) {
                // Clear existing elements
                clearWindElements(card);
                
                // Add more wind lines for enhanced effect
                for (let i = 0; i < 12; i++) {
                    const line = document.createElement('div');
                    line.className = 'wind-line';
                    line.style.top = `${10 + i * 15}px`;
                    line.style.animationDelay = `${i * 0.2}s`;
                    line.style.animationDuration = '2s';
                    card.appendChild(line);
                }
                
                // Add faster moving clouds
                const clouds = card.querySelectorAll('.cloud');
                clouds.forEach(cloud => {
                    cloud.style.animationDuration = '4s';
                });
            }

            function resetWindAnimation(card) {
                initWindAnimation(card);
            }

            function clearWindElements(card) {
                const windLines = card.querySelectorAll('.wind-line');
                windLines.forEach(line => line.remove());
            }

            // Rain Animation Functions
            function initRainAnimation(card) {
                // Clear existing raindrops
                clearRainElements(card);
                
                // Add raindrops
                for (let i = 0; i < 20; i++) {
                    createRaindrop(card, i);
                }
            }

            function enhanceRainAnimation(card) {
                // Clear existing raindrops
                clearRainElements(card);
                
                // Add more raindrops for enhanced effect
                for (let i = 0; i < 40; i++) {
                    createRaindrop(card, i, true);
                }
            }

            function resetRainAnimation(card) {
                initRainAnimation(card);
            }

            function createRaindrop(card, index, enhanced = false) {
                const drop = document.createElement('div');
                drop.className = 'raindrop';
                drop.style.left = `${Math.random() * 100}%`;
                drop.style.height = `${enhanced ? 15 + Math.random() * 15 : 10 + Math.random() * 10}px`;
                drop.style.opacity = `${0.5 + Math.random() * 0.5}`;
                drop.style.animationDuration = `${enhanced ? 0.5 + Math.random() * 0.5 : 1 + Math.random()}s`;
                drop.style.animationDelay = `${Math.random() * 2}s`;
                card.appendChild(drop);
            }

            function clearRainElements(card) {
                const raindrops = card.querySelectorAll('.raindrop');
                raindrops.forEach(drop => drop.remove());
            }

            // Sun Animation Functions
            function initSunAnimation(card) {
                // Clear existing sun rays
                clearSunElements(card);
                
                // Add sun rays
                const sun = card.querySelector('.sun');
                for (let i = 0; i < 8; i++) {
                    createSunRay(card, sun, i, 45 * i);
                }
            }

            function enhanceSunAnimation(card) {
                // Clear existing sun rays
                clearSunElements(card);
                
                // Add more sun rays for enhanced effect
                const sun = card.querySelector('.sun');
                sun.style.boxShadow = '0 0 80px #f39c12';
                
                for (let i = 0; i < 12; i++) {
                    createSunRay(card, sun, i, 30 * i, true);
                }
            }

            function resetSunAnimation(card) {
                const sun = card.querySelector('.sun');
                sun.style.boxShadow = '0 0 40px #f39c12';
                initSunAnimation(card);
            }

            function createSunRay(card, sun, index, angle, enhanced = false) {
                if (!sun) return;
                
                const ray = document.createElement('div');
                ray.className = 'sun-ray';
                ray.style.width = '2px';
                ray.style.height = enhanced ? '100px' : '60px';
                ray.style.left = '50%';
                ray.style.top = '70px';
                ray.style.transform = `translateX(-50%) rotate(${angle}deg)`;
                ray.style.animationDelay = `${index * 0.2}s`;
                card.appendChild(ray);
            }

            function clearSunElements(card) {
                const sunRays = card.querySelectorAll('.sun-ray');
                sunRays.forEach(ray => ray.remove());
            }

            // Snow Animation Functions
            function initSnowAnimation(card) {
                // Clear existing snowflakes
                clearSnowElements(card);
                
                // Add snowflakes
                for (let i = 0; i < 15; i++) {
                    createSnowflake(card, i);
                }
            }

            function enhanceSnowAnimation(card) {
                // Clear existing snowflakes
                clearSnowElements(card);
                
                // Add more snowflakes for enhanced effect
                for (let i = 0; i < 30; i++) {
                    createSnowflake(card, i, true);
                }
            }

            function resetSnowAnimation(card) {
                initSnowAnimation(card);
            }

            function createSnowflake(card, index, enhanced = false) {
                const flake = document.createElement('div');
                flake.className = 'snowflake';
                flake.innerHTML = '❄';
                flake.style.left = `${Math.random() * 100}%`;
                flake.style.fontSize = `${enhanced ? 10 + Math.random() * 15 : 8 + Math.random() * 12}px`;
                flake.style.opacity = `${0.6 + Math.random() * 0.4}`;
                flake.style.animationDuration = `${enhanced ? 3 + Math.random() * 3 : 5 + Math.random() * 5}s`;
                flake.style.animationDelay = `${Math.random() * 3}s`;
                card.appendChild(flake);
            }

            function clearSnowElements(card) {
                const snowflakes = card.querySelectorAll('.snowflake');
                snowflakes.forEach(flake => flake.remove());
            }
        });
    </script>
</body>
</html>