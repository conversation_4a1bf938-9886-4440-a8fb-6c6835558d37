try {
    $wordApp = New-Object -ComObject Word.Application
    $wordApp.Visible = $false
    
    $docPath = Join-Path -Path $PWD -ChildPath "Resume.docx"
    $pdfPath = Join-Path -Path $PWD -ChildPath "Resume.pdf"
    
    Write-Host "Opening document: $docPath"
    $doc = $wordApp.Documents.Open($docPath)
    
    Write-Host "Saving as PDF: $pdfPath"
    $doc.SaveAs([ref]$pdfPath, [ref]17)
    
    Write-Host "Closing document"
    $doc.Close()
    $wordApp.Quit()
    
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($doc) | Out-Null
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($wordApp) | Out-Null
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
    
    Write-Host "Conversion completed successfully"
    
    if (Test-Path $pdfPath) {
        Write-Host "PDF file created: $pdfPath"
    } else {
        Write-Host "Error: PDF file was not created"
    }
} catch {
    Write-Host "Error occurred: $_"
}
