// ==UserScript==
// @name         Google Batch Login Manager
// @namespace    http://tampermonkey.net/
// @version      1.1
// @description  Batch login for Google accounts with a convenient panel in the bottom right corner
// <AUTHOR>
// @match        *://*.google.com/*
// @match        *://accounts.google.com/*
// @grant        GM_addStyle
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// ==/UserScript==

(function() {
    'use strict';

    // CSS styles for the UI
    GM_addStyle(`
        #batch-login-panel {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 350px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            padding: 15px;
            z-index: 10001;
            font-family: Arial, sans-serif;
        }

        #panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            cursor: move;
            background-color: #4285f4;
            color: white;
            padding: 8px 12px;
            border-radius: 6px 6px 0 0;
            margin: -15px -15px 10px -15px;
        }

        #batch-login-panel h2 {
            margin: 0;
            font-size: 16px;
            color: white;
        }

        #accounts-input {
            width: 100%;
            height: 120px;
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            font-family: Arial, sans-serif;
            font-size: 13px;
            box-sizing: border-box;
        }

        .panel-buttons {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .panel-button {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            font-size: 13px;
            transition: background-color 0.2s;
        }

        #start-button {
            background-color: #4285f4;
            color: white;
            flex: 1;
            margin-right: 5px;
        }

        #start-button:hover {
            background-color: #3367d6;
        }

        #stop-button {
            background-color: #ea4335;
            color: white;
            flex: 1;
            margin-right: 5px;
        }

        #stop-button:hover {
            background-color: #d33426;
        }

        #goto-login-button {
            background-color: #34a853;
            color: white;
            flex: 1;
        }

        #goto-login-button:hover {
            background-color: #2d9144;
        }

        #close-button {
            background: none;
            color: white;
            font-size: 18px;
            padding: 0 5px;
            line-height: 1;
        }

        #close-button:hover {
            color: #f1f1f1;
        }

        #status-area {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            height: 80px;
            overflow-y: auto;
            margin-bottom: 10px;
            font-family: monospace;
            font-size: 12px;
            background-color: #f9f9f9;
        }

        #current-account {
            font-size: 12px;
            color: #666;
            font-weight: bold;
            text-align: center;
        }
    `);

    // Global variables
    let isRunning = false;
    let accountQueue = [];
    let currentAccount = null;

    // Create UI elements
    function createUI() {
        // Create the panel
        const panel = document.createElement('div');
        panel.id = 'batch-login-panel';
        panel.innerHTML = `
            <div id="panel-header">
                <h2>Google Batch Login</h2>
                <button id="close-button" class="panel-button">×</button>
            </div>
            <textarea id="accounts-input" placeholder="Enter email and password separated by four spaces (email    password)&#10;One account per line"></textarea>
            <div class="panel-buttons">
                <button id="start-button" class="panel-button">Start Login</button>
                <button id="stop-button" class="panel-button">Stop</button>
                <button id="goto-login-button" class="panel-button">Go to Login Page</button>
            </div>
            <div id="status-area"></div>
            <div id="current-account"></div>
        `;
        document.body.appendChild(panel);

        // Add event listeners
        document.getElementById('start-button').addEventListener('click', startLogin);
        document.getElementById('stop-button').addEventListener('click', stopLogin);
        document.getElementById('close-button').addEventListener('click', () => panel.remove());
        document.getElementById('goto-login-button').addEventListener('click', () => {
            window.location.href = 'https://accounts.google.com/v3/signin/identifier?hl=en&continue=https%3A%2F%2Fwww.google.com.hk%2F&ec=GAlAmgQ&authuser=0&ddm=1&flowName=GlifWebSignIn&flowEntry=AddSession';
        });

        // Load saved accounts if any
        const savedAccounts = GM_getValue('accountList', '');
        document.getElementById('accounts-input').value = savedAccounts;

        // Make panel draggable
        makeDraggable(panel, document.getElementById('panel-header'));
    }

    // Make an element draggable
    function makeDraggable(element, handle) {
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;

        handle.onmousedown = dragMouseDown;

        function dragMouseDown(e) {
            e.preventDefault();
            // Get the mouse cursor position at startup
            pos3 = e.clientX;
            pos4 = e.clientY;
            document.onmouseup = closeDragElement;
            // Call a function whenever the cursor moves
            document.onmousemove = elementDrag;
        }

        function elementDrag(e) {
            e.preventDefault();
            // Calculate the new cursor position
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;
            // Set the element's new position
            element.style.top = (element.offsetTop - pos2) + "px";
            element.style.left = (element.offsetLeft - pos1) + "px";
            element.style.bottom = "auto";
            element.style.right = "auto";
        }

        function closeDragElement() {
            // Stop moving when mouse button is released
            document.onmouseup = null;
            document.onmousemove = null;

            // Save the current position
            saveAccounts();
        }
    }

    // Save accounts to storage
    function saveAccounts() {
        const accounts = document.getElementById('accounts-input').value;
        GM_setValue('accountList', accounts);
    }

    // Start the login process
    function startLogin() {
        if (isRunning) return;

        const accountsText = document.getElementById('accounts-input').value.trim();
        if (!accountsText) {
            updateStatus('No accounts provided. Please enter accounts in the format: email    password');
            return;
        }

        // Parse accounts
        accountQueue = accountsText.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0)
            .map(line => {
                const parts = line.split(/\s{4}/);
                if (parts.length >= 2) {
                    return {
                        email: parts[0].trim(),
                        password: parts[1].trim()
                    };
                }
                return null;
            })
            .filter(account => account !== null);

        if (accountQueue.length === 0) {
            updateStatus('No valid accounts found. Please use the format: email    password');
            return;
        }

        isRunning = true;
        updateStatus(`Starting login process with ${accountQueue.length} accounts`);

        // Save the updated account list
        saveAccounts();

        // Navigate to the login page if we're not already there
        if (!window.location.href.includes('accounts.google.com/v3/signin')) {
            window.location.href = 'https://accounts.google.com/v3/signin/identifier?hl=en&continue=https%3A%2F%2Fwww.google.com.hk%2F&ec=GAlAmgQ&authuser=0&ddm=1&flowName=GlifWebSignIn&flowEntry=AddSession';
        } else {
            // If we're already on the login page, start processing
            processNextAccount();
        }
    }

    // Stop the login process
    function stopLogin() {
        isRunning = false;
        currentAccount = null;
        updateStatus('Login process stopped by user');
    }

    // Update status area
    function updateStatus(message) {
        const statusArea = document.getElementById('status-area');
        const timestamp = new Date().toLocaleTimeString();
        statusArea.innerHTML += `<div>[${timestamp}] ${message}</div>`;
        statusArea.scrollTop = statusArea.scrollHeight;
    }

    // Update current account display
    function updateCurrentAccount() {
        const currentAccountDiv = document.getElementById('current-account');
        if (currentAccount) {
            currentAccountDiv.textContent = `Current: ${currentAccount.email}`;
        } else {
            currentAccountDiv.textContent = '';
        }
    }

    // Process the next account in the queue
    function processNextAccount() {
        if (!isRunning || accountQueue.length === 0) {
            if (isRunning) {
                updateStatus('All accounts processed');
                isRunning = false;
            }
            return;
        }

        currentAccount = accountQueue.shift();
        updateCurrentAccount();
        updateStatus(`Processing account: ${currentAccount.email}`);

        // Update the textarea with remaining accounts
        document.getElementById('accounts-input').value = accountQueue
            .map(account => `${account.email}    ${account.password}`)
            .join('\n');

        // Save the updated account list
        saveAccounts();

        // Check current page and proceed accordingly
        handleCurrentPage();
    }

    // Handle the current page in the login flow
    function handleCurrentPage() {
        if (!isRunning || !currentAccount) return;

        // Check if we're on the email input page
        const emailInput = document.querySelector('input[type="email"]') ||
                          document.querySelector('input[aria-label="Email or phone"]') ||
                          document.querySelector('input#identifierId');
        if (emailInput) {
            waitForElement('input[type="email"], input[aria-label="Email or phone"], input#identifierId', element => {
                typeIntoField(element, currentAccount.email);
                // Look for the Next button with multiple possible selectors
                waitForElement('button:contains("Next"), button[jsname="LgbsSe"], button.VfPpkd-LgbsSe-OWXEXe-k8QpJ, button.VfPpkd-LgbsSe, button:contains("Next"), div[role="button"]:contains("Next"), span:contains("Next")', nextButton => {
                    setTimeout(() => {
                        nextButton.click();
                        updateStatus('Email entered, proceeding to password');
                    }, 1000);
                });
            });
            return;
        }

        // Check if we're on the password input page
        const passwordInput = document.querySelector('input[type="password"]') ||
                             document.querySelector('input[aria-label="Enter your password"]') ||
                             document.querySelector('input[name="Passwd"]');
        if (passwordInput) {
            waitForElement('input[type="password"], input[aria-label="Enter your password"], input[name="Passwd"]', element => {
                typeIntoField(element, currentAccount.password);
                // Look for the Next button with multiple possible selectors
                waitForElement('button:contains("Next"), button[jsname="LgbsSe"], button.VfPpkd-LgbsSe-OWXEXe-k8QpJ, button.VfPpkd-LgbsSe, button:contains("Next"), div[role="button"]:contains("Next"), span:contains("Next")', nextButton => {
                    setTimeout(() => {
                        nextButton.click();
                        updateStatus('Password entered, proceeding');
                    }, 1000);
                });
            });
            return;
        }

        // Check if we're on the "I understand" page
        const confirmButton = document.querySelector('input[type="submit"][name="confirm"][value="I understand"]') ||
                             document.querySelector('button:contains("I understand")') ||
                             document.querySelector('input[value="I understand"]');
        if (confirmButton) {
            waitForElement('input[type="submit"][name="confirm"][value="I understand"], button:contains("I understand"), input[value="I understand"]', element => {
                setTimeout(() => {
                    element.click();
                    updateStatus('Clicked "I understand"');
                }, 1000);
            });
            return;
        }

        // Check if we're on the "Do this later" page
        const laterLink = document.querySelector('a[href*="DoThisLater"]') ||
                          document.querySelector('a:contains("Do this later")') ||
                          document.querySelector('a[href*="aodrpl=1"]') ||
                          document.querySelector('button:contains("Do this later")') ||
                          document.querySelector('div[role="button"]:contains("Do this later")');
        if (laterLink) {
            setTimeout(() => {
                laterLink.click();
                updateStatus('Clicked "Do this later"');

                // Account login completed
                updateStatus(`Successfully logged in: ${currentAccount.email}`);
                currentAccount = null;
                updateCurrentAccount();

                // Process next account after a delay
                setTimeout(() => {
                    if (isRunning) {
                        // Navigate back to login page for next account
                        window.location.href = 'https://accounts.google.com/v3/signin/identifier?hl=en&continue=https%3A%2F%2Fwww.google.com.hk%2F&ec=GAlAmgQ&authuser=0&ddm=1&flowName=GlifWebSignIn&flowEntry=AddSession';
                    }
                }, 2000);
            }, 1000);
            return;
        }

        // Check for any "Next" button on the page (for handling unexpected steps)
        let anyNextButton = document.querySelector('button:contains("Next")') ||
                           document.querySelector('div[role="button"]:contains("Next")') ||
                           document.querySelector('input[value="Next"]');

        // Try to find a span with "Next" text and get its parent button
        const nextSpan = document.querySelector('span:contains("Next")');
        if (!anyNextButton && nextSpan) {
            anyNextButton = nextSpan.closest('button') || nextSpan.parentElement;
        }
        if (anyNextButton) {
            setTimeout(() => {
                anyNextButton.click();
                updateStatus('Clicked a "Next" button on an intermediate page');
            }, 1000);
            return;
        }

        // If we're on Google homepage or another Google page after successful login
        if (window.location.hostname.includes('google.com') &&
            !window.location.hostname.includes('accounts.google.com')) {
            updateStatus(`Successfully logged in: ${currentAccount.email}`);
            currentAccount = null;
            updateCurrentAccount();

            // Process next account after a delay
            setTimeout(() => {
                if (isRunning) {
                    // Navigate back to login page for next account
                    window.location.href = 'https://accounts.google.com/v3/signin/identifier?hl=en&continue=https%3A%2F%2Fwww.google.com.hk%2F&ec=GAlAmgQ&authuser=0&ddm=1&flowName=GlifWebSignIn&flowEntry=AddSession';
                }
            }, 2000);
            return;
        }

        // If we couldn't identify the current page
        updateStatus('Could not identify current page in login flow');

        // Debug information
        const pageUrl = window.location.href;
        updateStatus(`Current URL: ${pageUrl}`);

        // Try to find any buttons on the page that might help
        const allButtons = document.querySelectorAll('button, input[type="submit"], div[role="button"]');
        if (allButtons.length > 0) {
            updateStatus(`Found ${allButtons.length} potential clickable elements`);
            // Try to click a button that might advance the flow
            for (const btn of allButtons) {
                const btnText = btn.textContent || btn.value || '';
                if (btnText.includes('Next') || btnText.includes('Continue') ||
                    btnText.includes('Agree') || btnText.includes('Accept') ||
                    btnText.includes('Skip') || btnText.includes('Later')) {
                    updateStatus(`Attempting to click button with text: ${btnText}`);
                    setTimeout(() => {
                        btn.click();
                    }, 1000);
                    return;
                }
            }
        }
    }

    // Helper function to wait for an element to be available
    function waitForElement(selector, callback, maxAttempts = 20, interval = 500) {
        let attempts = 0;

        const checkElement = () => {
            // Handle complex selectors with :contains
            if (selector.includes(':contains(')) {
                // Split by commas that are not inside parentheses
                const selectorParts = selector.split(/,(?![^(]*\))/);

                for (const part of selectorParts) {
                    if (part.includes(':contains(')) {
                        // Extract the base selector and the text to search for
                        const baseSelectorMatch = part.match(/(.*?):contains\(["'](.*?)["']\)/);
                        if (baseSelectorMatch) {
                            const baseSelector = baseSelectorMatch[1].trim();
                            const searchText = baseSelectorMatch[2];

                            // Find all elements matching the base selector
                            const elements = document.querySelectorAll(baseSelector);
                            for (const el of elements) {
                                if (el.textContent.includes(searchText)) {
                                    callback(el);
                                    return;
                                }
                            }
                        }
                    } else {
                        // Regular selector
                        const element = document.querySelector(part.trim());
                        if (element) {
                            callback(element);
                            return;
                        }
                    }
                }
            } else {
                // Regular selector
                const element = document.querySelector(selector);
                if (element) {
                    callback(element);
                    return;
                }
            }

            attempts++;
            if (attempts < maxAttempts) {
                setTimeout(checkElement, interval);
            } else {
                updateStatus(`Element not found: ${selector}`);
                if (isRunning) {
                    // Try to continue with next account
                    currentAccount = null;
                    updateCurrentAccount();
                    processNextAccount();
                }
            }
        };

        checkElement();
    }

    // Helper function to type into a field like a human
    function typeIntoField(element, text) {
        element.focus();
        element.value = '';

        // Simulate typing
        let i = 0;
        const typeChar = () => {
            if (i < text.length) {
                element.value += text.charAt(i);
                i++;
                setTimeout(typeChar, Math.random() * 100 + 50);
            }
        };

        typeChar();
    }

    // Initialize the script
    function initialize() {
        // Create the UI
        createUI();

        // Check if we're in the middle of a login process
        const savedRunning = GM_getValue('isRunning', false);
        const savedQueue = GM_getValue('accountQueue', []);
        const savedCurrentAccount = GM_getValue('currentAccount', null);

        if (savedRunning && (savedQueue.length > 0 || savedCurrentAccount)) {
            isRunning = true;
            accountQueue = savedQueue;
            currentAccount = savedCurrentAccount;

            updateStatus('Resuming previous login session...');

            if (currentAccount) {
                updateCurrentAccount();
                setTimeout(() => handleCurrentPage(), 1000);
            } else {
                setTimeout(() => processNextAccount(), 1000);
            }
        } else {
            updateStatus('Ready to start batch login. Enter accounts above and click "Start Login".');
        }
    }

    // Save state before page unload
    window.addEventListener('beforeunload', () => {
        if (isRunning) {
            GM_setValue('isRunning', isRunning);
            GM_setValue('accountQueue', accountQueue);
            GM_setValue('currentAccount', currentAccount);
        } else {
            GM_deleteValue('isRunning');
            GM_deleteValue('accountQueue');
            GM_deleteValue('currentAccount');
        }

        // Always save the accounts
        saveAccounts();
    });

    // Initialize after page load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // Check for login flow pages and handle them
    if (window.location.hostname.includes('accounts.google.com')) {
        // Wait a bit for the page to fully load
        setTimeout(() => {
            if (isRunning) {
                if (currentAccount) {
                    handleCurrentPage();
                } else {
                    processNextAccount();
                }
            }
        }, 1500);
    }

    // Add keyboard shortcut (Alt+L) to show/hide the panel
    document.addEventListener('keydown', (e) => {
        if (e.altKey && e.key === 'l') {
            const panel = document.getElementById('batch-login-panel');
            if (panel) {
                if (panel.style.display === 'none') {
                    panel.style.display = 'block';
                } else {
                    panel.style.display = 'none';
                }
            } else {
                initialize();
            }
        }
    });
})();
