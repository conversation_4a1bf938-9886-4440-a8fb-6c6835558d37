// ==UserScript==
// @name         Random Name Generator
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Provides a library of 100 random names that can be copied with a click
// <AUTHOR>
// @match        https://console.anthropic.com/*
// @grant        GM_setClipboard
// ==/UserScript==

(function() {
    'use strict';

    // Configuration
    const config = {
        buttonColor: '#10a37f', // Button background color
        buttonTextColor: 'white', // Button text color
        showNotification: true // Show notification when name is copied
    };

    // Library of 100 random names (first and last names combined)
    const nameLibrary = [
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON> <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON> <PERSON>", "<PERSON> <PERSON>", "<PERSON> <PERSON>", "<PERSON>", "<PERSON> <PERSON>",
        "<PERSON> <PERSON>", "<PERSON>", "<PERSON> <PERSON>", "<PERSON> <PERSON>", "<PERSON>",
        "<PERSON> <PERSON>", "<PERSON> <PERSON>", "<PERSON> <PERSON>", "<PERSON> <PERSON>", "<PERSON> <PERSON>",
        "<PERSON> <PERSON>", "<PERSON> <PERSON>", "Ethan Cooper", "Mason Richardson", "Logan Cox",
        "Jacob Howard", "Lucas Ward", "Jackson Torres", "Jack Peterson", "Aiden Gray",
        "Chloe James", "Zoe Watson", "Lily Brooks", "Layla Kelly", "Lillian Sanders",
        "Aria Price", "Zoey Bennett", "Nora Wood", "Hannah Barnes", "Lily Ross",
        "Oliver Henderson", "Elijah Coleman", "Lucas Jenkins", "Mason Perry", "Logan Powell",
        "Alexander Long", "Ethan Patterson", "Jacob Hughes", "Michael Flores", "Benjamin Washington",
        "Mia Butler", "Charlotte Simmons", "Amelia Foster", "Harper Bryant", "Evelyn Russell",
        "Abigail Griffin", "Emily Diaz", "Elizabeth Hayes", "Sofia Myers", "Victoria Ford"
    ];

    // Function to create a notification when a name is copied
    function showNotification(message, duration = 2000) {
        // Create notification element
        const notification = document.createElement('div');
        notification.textContent = message;
        notification.style.position = 'fixed';
        notification.style.bottom = '20px';
        notification.style.left = '50%';
        notification.style.transform = 'translateX(-50%)';
        notification.style.backgroundColor = '#333';
        notification.style.color = 'white';
        notification.style.padding = '10px 20px';
        notification.style.borderRadius = '5px';
        notification.style.zIndex = '10000';
        notification.style.opacity = '0';
        notification.style.transition = 'opacity 0.3s ease';

        // Add to document
        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Remove after duration
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, duration);
    }

    // Function to copy text to clipboard
    function copyToClipboard(text) {
        // Try to use the Tampermonkey API first
        if (typeof GM_setClipboard !== 'undefined') {
            GM_setClipboard(text);
            return true;
        }

        // Fallback to using document.execCommand
        try {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.style.position = 'fixed';
            textarea.style.opacity = '0';
            document.body.appendChild(textarea);
            textarea.select();
            const success = document.execCommand('copy');
            document.body.removeChild(textarea);
            return success;
        } catch (e) {
            console.error('Failed to copy text: ', e);
            return false;
        }
    }

    // Function to get a random name from the library
    function getRandomName() {
        const randomIndex = Math.floor(Math.random() * nameLibrary.length);
        return nameLibrary[randomIndex];
    }



    // Create the main button to copy a random name
    function createMainButton() {
        // Remove existing button if it exists
        const existingButton = document.getElementById('random-name-btn');
        if (existingButton) {
            existingButton.remove();
        }

        // Create button element
        const button = document.createElement('button');
        button.id = 'random-name-btn';
        button.textContent = 'Copy Name';
        button.title = 'Click to copy a random name to clipboard';

        // Apply styles as inline CSS to ensure they take precedence
        const buttonStyles = {
            position: 'fixed',
            zIndex: '99999', // Higher z-index to ensure it's on top
            padding: '10px 15px',
            fontSize: '16px',
            fontWeight: 'bold',
            backgroundColor: config.buttonColor,
            color: config.buttonTextColor,
            border: 'none',
            borderRadius: '50px',
            cursor: 'pointer',
            boxShadow: '0 3px 8px rgba(0, 0, 0, 0.3)',
            transition: 'all 0.3s ease',
            fontFamily: 'Arial, sans-serif',
            // Position in the middle of the right edge
            top: '50%',
            right: '20px',
            marginTop: '0',
            marginRight: '0',
            transform: 'translateY(-50%)'
        };

        // Apply all styles
        Object.assign(button.style, buttonStyles);

        // Add hover effect
        button.onmouseover = function() {
            this.style.transform = 'translateY(-50%) scale(1.05)';
            this.style.boxShadow = '0 5px 12px rgba(0, 0, 0, 0.4)';
        };

        button.onmouseout = function() {
            this.style.transform = 'translateY(-50%)';
            this.style.boxShadow = '0 3px 8px rgba(0, 0, 0, 0.3)';
        };

        // Add click event to copy a random name
        button.addEventListener('click', function() {
            // Get a random name
            const randomName = getRandomName();

            // Copy to clipboard
            const success = copyToClipboard(randomName);

            // Show notification
            if (success && config.showNotification) {
                showNotification(`Copied: ${randomName}`);
            }

            // Visual feedback on click
            this.style.transform = 'translateY(-50%) scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-50%)';
            }, 100);

            // Change button text temporarily to show success
            const originalText = this.textContent;
            this.textContent = '✓ Copied!';
            setTimeout(() => {
                this.textContent = originalText;
            }, 1500);
        });

        // Add button to the page
        document.body.appendChild(button);
    }

    // Initialize the script
    function initialize() {
        // Create the main button
        createMainButton();
    }

    // Run the initialization when the page is fully loaded
    window.addEventListener('load', initialize);

    // Also run when URL changes (for single-page applications)
    let lastUrl = window.location.href;
    new MutationObserver(() => {
        const currentUrl = window.location.href;
        if (currentUrl !== lastUrl) {
            lastUrl = currentUrl;
            // Ensure button exists on new pages
            createMainButton();
        }
    }).observe(document, { subtree: true, childList: true });

    // Run immediately if the page is already loaded
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        initialize();
    }

    // Force button recreation every 2 seconds to ensure it stays visible
    // This helps overcome any CSS or JS that might be hiding or removing our button
    setInterval(createMainButton, 2000);


})();
