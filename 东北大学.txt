登录界面
https://enroll.northeastern.edu/account/login?r=https%3a%2f%2fenroll.northeastern.edu%2fapply%2f&cookie=1
邮箱密码从txt中读取，每次运行程序不能从头开始读取
邮箱
<input id="email" maxlength="64" name="email" size="48" style="width: 200px;" value="" type="email" data-validate="{ required: true, format: 'email', help: 'Email address is required.' }" autocomplete="off" spellcheck="false" class="maxlength_enabled" required="required">
密码
<input id="password" maxlength="64" name="password" size="48" style="width: 200px;" type="password" value="" data-validate="{ required: true, help: 'Password is required.' }" autocomplete="off" spellcheck="false" required="required">
登录按钮
<button class="default" onclick="if (FW.Validate(this)) FW.Postback(this); return false;" type="submit">Login</button>

下一个界面 https://enroll.northeastern.edu/apply/
点击申请类型按钮 <a href="//" id="start_application_link" onclick="return FW.Lazy.Popup(this);" data-href="?cmd=detail">Start New Application</a>

等弹窗出来后再点击创建申请按钮
<button class="default" onclick="if (FW.Validate(this)) FW.Lazy.Commit(this, { cmd: 'save' });" type="button">Create Application</button>

等待弹窗出来点击打开申请按钮
<button class="default" type="button" onclick="var el = $(this).parents('form').find('input[name = &quot;round&quot;]:enabled'); if (el.length > 0) if (el.filter(':checked').length == 0) { alert('Please select an application round.'); el.eq(0).focus(); return; } FW.Lazy.Commit(this, { cmd: 'save' });">Open Application</button>


