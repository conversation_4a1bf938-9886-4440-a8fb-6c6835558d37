// ==UserScript==
// @name         Anthropic Organization ID Extractor
// @namespace    http://tampermonkey.net/
// @version      1.4
// @description  Adds a button to extract and copy the organization ID from Anthropic console and logout
// <AUTHOR>
// @match        https://console.anthropic.com/*
// @grant        GM_setClipboard
// @grant        GM_addStyle
// ==/UserScript==

(function() {
    'use strict';

    // Add custom styles
    GM_addStyle(`
        .extractor-btn {
            position: fixed;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            transition: all 0.3s ease;
        }
        .extractor-btn:hover {
            transform: scale(1.05);
        }
        .org-id-btn {
            bottom: 20px;
            right: 20px;
            background-color: #6366f1;
        }
        .org-id-btn:hover {
            background-color: #4f46e5;
        }

        .extractor-notification {
            position: fixed;
            bottom: 80px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
            max-width: 300px;
            word-break: break-all;
        }
        .extractor-notification.show {
            opacity: 1;
            transform: translateY(0);
        }
    `);

    // Check if we're on the organization settings page
    function isOnOrgSettingsPage() {
        return window.location.href.includes('/settings/organization');
    }

    // Check if we're on the account type selection page
    function isOnAccountTypePage() {
        return window.location.href.includes('/onboarding') ||
               window.location.href.includes('/create');
    }

    // URL for organization settings page
    const ORG_SETTINGS_URL = 'https://console.anthropic.com/settings/organization';

    // Function to click the Individual button
    function clickIndividualButton() {
        if (!isOnAccountTypePage()) return false;

        // Find the Individual button by its text content
        const buttons = document.querySelectorAll('button');
        for (const button of buttons) {
            if (button.textContent.includes('Individual')) {
                showNotification('Clicking Individual button...');
                button.click();
                return true;
            }
        }

        // Alternative method: find by h3 element with "Individual" text
        const h3Elements = document.querySelectorAll('h3');
        for (const h3 of h3Elements) {
            if (h3.textContent.trim() === 'Individual') {
                const button = h3.closest('button');
                if (button) {
                    showNotification('Clicking Individual button...');
                    button.click();
                    return true;
                }
            }
        }

        return false;
    }

    // Function to extract organization ID
    function extractOrganizationId() {
        if (!isOnOrgSettingsPage()) return null;

        const orgIdElement = document.querySelector('div.text-text-300.inline-flex.items-center.gap-2.text-xs');
        if (orgIdElement) {
            const text = orgIdElement.textContent;
            const match = text.match(/Organization ID: ([0-9a-f-]+)/);
            if (match && match[1]) {
                return match[1].trim();
            }
        }
        return null;
    }

    // Function to show notification
    function showNotification(message) {
        // Remove any existing notification
        const existingNotification = document.querySelector('.extractor-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // Create new notification
        const notification = document.createElement('div');
        notification.className = 'extractor-notification';
        notification.textContent = message;
        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Hide notification after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    // Function to handle extraction with navigation if needed
    function handleExtraction(extractionFunction) {
        // First check if we're on the account type page
        if (isOnAccountTypePage()) {
            // Try to click the Individual button
            if (clickIndividualButton()) {
                // Store the extraction function to run after navigation
                sessionStorage.setItem('pendingExtraction', extractionFunction.name);
                return;
            }
        }
        
        // Then proceed with normal extraction flow
        if (isOnOrgSettingsPage()) {
            // Already on the right page, extract immediately
            extractionFunction();
        } else {
            // Navigate to org settings page first
            showNotification('Navigating to organization settings...');
            window.location.href = ORG_SETTINGS_URL;

            // Store the extraction function to run after navigation
            sessionStorage.setItem('pendingExtraction', extractionFunction.name);
        }
    }

    // Function to handle logout
    function logoutUser() {
        showNotification('Logging out...');
        setTimeout(() => {
            window.location.href = '/logout';
        }, 1000);
    }

    // Function to copy organization ID to clipboard
    function copyOrganizationId() {
        const orgId = extractOrganizationId();
        if (orgId) {
            GM_setClipboard(orgId);
            showNotification('Organization ID copied to clipboard!\n' + orgId);
            // Wait a moment before logging out to ensure the notification is seen
            setTimeout(() => {
                logoutUser();
            }, 1500);
        } else {
            showNotification('Could not find Organization ID!');
        }
    }

    // Function to add the buttons to the page
    function addExtractButtons() {
        // ID button
        const idButton = document.createElement('button');
        idButton.className = 'extractor-btn org-id-btn';
        idButton.innerHTML = 'ID';
        idButton.title = 'Copy Organization ID and Logout';
        idButton.addEventListener('click', function() {
            handleExtraction(copyOrganizationId);
        });
        document.body.appendChild(idButton);
    }

    // Check for pending extraction after page load
    function checkPendingExtraction() {
        // Check if we're on the account type page
        if (isOnAccountTypePage()) {
            const pendingExtraction = sessionStorage.getItem('pendingExtraction');
            if (pendingExtraction) {
                // Try to click the Individual button
                setTimeout(() => {
                    if (clickIndividualButton()) {
                        // Keep the pending extraction in session storage
                        return;
                    }
                }, 1000);
            }
        }
        
        // Check if we're on the org settings page
        if (isOnOrgSettingsPage()) {
            const pendingExtraction = sessionStorage.getItem('pendingExtraction');
            if (pendingExtraction) {
                sessionStorage.removeItem('pendingExtraction');
                // Wait for the page to be fully loaded and rendered
                setTimeout(() => {
                    if (pendingExtraction === 'copyOrganizationId') copyOrganizationId();
                }, 2000);
            }
        }
    }

    // Wait for the page to fully load
    window.addEventListener('load', function() {
        // Add a small delay to ensure the page is fully rendered
        setTimeout(() => {
            addExtractButtons();
            checkPendingExtraction();
        }, 1000);
    });

    // Also try to add the buttons immediately in case the page is already loaded
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        setTimeout(() => {
            addExtractButtons();
            checkPendingExtraction();
        }, 1000);
    }
})();
