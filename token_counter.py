#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
销售培训系统文件夹Token统计工具
统计指定文件夹中所有文本文件的token数量
"""

import os
import sys
import tiktoken
from pathlib import Path
import argparse
from typing import Dict, List, Tuple

class TokenCounter:
    def __init__(self, encoding_name: str = "cl100k_base"):
        """
        初始化Token计数器
        
        Args:
            encoding_name: tiktoken编码名称，默认使用cl100k_base (GPT-4/GPT-3.5-turbo)
        """
        try:
            self.encoding = tiktoken.get_encoding(encoding_name)
        except Exception as e:
            print(f"警告: 无法加载编码 {encoding_name}, 使用默认编码: {e}")
            self.encoding = tiktoken.get_encoding("cl100k_base")
        
        self.encoding_name = encoding_name
        self.total_tokens = 0
        self.file_stats = {}
    
    def count_tokens_in_text(self, text: str) -> int:
        """
        计算文本中的token数量
        
        Args:
            text: 要计算的文本
            
        Returns:
            token数量
        """
        try:
            tokens = self.encoding.encode(text)
            return len(tokens)
        except Exception as e:
            print(f"计算token时出错: {e}")
            return 0
    
    def read_file_with_encoding(self, file_path: Path) -> str:
        """
        尝试用不同编码读取文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容
        """
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'latin-1']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.read()
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"读取文件 {file_path} 时出错: {e}")
                return ""
        
        print(f"警告: 无法读取文件 {file_path}，尝试了所有编码")
        return ""
    
    def count_tokens_in_file(self, file_path: Path) -> Tuple[int, int]:
        """
        计算单个文件的token数量
        
        Args:
            file_path: 文件路径
            
        Returns:
            (token数量, 字符数量)
        """
        try:
            content = self.read_file_with_encoding(file_path)
            if not content:
                return 0, 0
            
            token_count = self.count_tokens_in_text(content)
            char_count = len(content)
            
            return token_count, char_count
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            return 0, 0
    
    def scan_directory(self, directory: Path, file_extensions: List[str] = None) -> Dict:
        """
        扫描目录中的所有文件并统计token
        
        Args:
            directory: 要扫描的目录
            file_extensions: 要处理的文件扩展名列表，None表示处理所有文本文件
            
        Returns:
            统计结果字典
        """
        if file_extensions is None:
            file_extensions = ['.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml', '.csv']
        
        results = {
            'files': {},
            'total_tokens': 0,
            'total_chars': 0,
            'total_files': 0,
            'encoding': self.encoding_name
        }
        
        if not directory.exists():
            print(f"错误: 目录 {directory} 不存在")
            return results
        
        print(f"开始扫描目录: {directory}")
        print(f"使用编码: {self.encoding_name}")
        print("-" * 60)
        
        for file_path in directory.rglob('*'):
            if file_path.is_file():
                # 检查文件扩展名
                if file_extensions and file_path.suffix.lower() not in file_extensions:
                    continue
                
                print(f"处理文件: {file_path.name}")
                token_count, char_count = self.count_tokens_in_file(file_path)
                
                if token_count > 0:
                    relative_path = file_path.relative_to(directory)
                    results['files'][str(relative_path)] = {
                        'tokens': token_count,
                        'chars': char_count,
                        'size_kb': file_path.stat().st_size / 1024
                    }
                    
                    results['total_tokens'] += token_count
                    results['total_chars'] += char_count
                    results['total_files'] += 1
                    
                    print(f"  Token数量: {token_count:,}")
                    print(f"  字符数量: {char_count:,}")
                    print(f"  文件大小: {file_path.stat().st_size / 1024:.2f} KB")
                    print()
        
        return results
    
    def print_summary(self, results: Dict):
        """
        打印统计摘要
        
        Args:
            results: 统计结果
        """
        print("=" * 60)
        print("统计摘要")
        print("=" * 60)
        print(f"总文件数: {results['total_files']}")
        print(f"总Token数: {results['total_tokens']:,}")
        print(f"总字符数: {results['total_chars']:,}")
        print(f"平均每文件Token数: {results['total_tokens'] / max(results['total_files'], 1):.0f}")
        print(f"使用编码: {results['encoding']}")
        print()
        
        # 按token数量排序显示前10个文件
        if results['files']:
            print("Token数量最多的文件 (前10个):")
            print("-" * 60)
            sorted_files = sorted(results['files'].items(), 
                                key=lambda x: x[1]['tokens'], reverse=True)
            
            for i, (file_path, stats) in enumerate(sorted_files[:10], 1):
                print(f"{i:2d}. {file_path}")
                print(f"    Token: {stats['tokens']:,}, 字符: {stats['chars']:,}, "
                      f"大小: {stats['size_kb']:.2f} KB")
    
    def save_results_to_file(self, results: Dict, output_file: Path):
        """
        将结果保存到文件
        
        Args:
            results: 统计结果
            output_file: 输出文件路径
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("销售培训系统Token统计报告\n")
                f.write("=" * 60 + "\n\n")
                f.write(f"统计时间: {Path().cwd()}\n")
                f.write(f"使用编码: {results['encoding']}\n")
                f.write(f"总文件数: {results['total_files']}\n")
                f.write(f"总Token数: {results['total_tokens']:,}\n")
                f.write(f"总字符数: {results['total_chars']:,}\n")
                f.write(f"平均每文件Token数: {results['total_tokens'] / max(results['total_files'], 1):.0f}\n\n")
                
                f.write("详细文件统计:\n")
                f.write("-" * 60 + "\n")
                
                sorted_files = sorted(results['files'].items(), 
                                    key=lambda x: x[1]['tokens'], reverse=True)
                
                for file_path, stats in sorted_files:
                    f.write(f"文件: {file_path}\n")
                    f.write(f"  Token数量: {stats['tokens']:,}\n")
                    f.write(f"  字符数量: {stats['chars']:,}\n")
                    f.write(f"  文件大小: {stats['size_kb']:.2f} KB\n\n")
            
            print(f"结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存结果时出错: {e}")


def main():
    parser = argparse.ArgumentParser(description='统计文件夹中所有文件的Token数量')
    parser.add_argument('directory', nargs='?', default='销售培训系统', 
                       help='要统计的目录路径 (默认: 销售培训系统)')
    parser.add_argument('--encoding', default='cl100k_base',
                       help='tiktoken编码名称 (默认: cl100k_base)')
    parser.add_argument('--extensions', nargs='+', 
                       default=['.txt', '.md'],
                       help='要处理的文件扩展名 (默认: .txt .md)')
    parser.add_argument('--output', '-o', 
                       help='输出结果到文件')
    
    args = parser.parse_args()
    
    # 创建Token计数器
    counter = TokenCounter(args.encoding)
    
    # 扫描目录
    directory = Path(args.directory)
    results = counter.scan_directory(directory, args.extensions)
    
    # 打印摘要
    counter.print_summary(results)
    
    # 保存结果到文件
    if args.output:
        counter.save_results_to_file(results, Path(args.output))
    else:
        # 默认保存到当前目录
        output_file = Path(f"token_stats_{directory.name}.txt")
        counter.save_results_to_file(results, output_file)


if __name__ == "__main__":
    main()
