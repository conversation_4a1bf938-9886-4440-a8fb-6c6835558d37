# 销售培训系统Token统计工具

这个工具用于统计销售培训系统文件夹中所有文本文件的Token数量。

## 文件说明

### 1. token_counter.py (完整版)
- 使用tiktoken库进行精确的Token计算
- 支持多种文件格式 (.txt, .md, .py, .js等)
- 支持多种文本编码 (UTF-8, GBK, GB2312等)
- 提供详细的统计报告
- 可自定义编码方式和文件类型

### 2. simple_token_counter.py (简化版)
- 无需安装额外依赖
- 使用简单算法估算Token数量
- 专门针对中文文本优化
- 快速运行，适合快速统计

### 3. run_token_counter.bat (批处理脚本)
- 自动安装依赖
- 一键运行完整版统计工具

## 使用方法

### 方法一：使用批处理脚本（推荐）
1. 双击运行 `run_token_counter.bat`
2. 脚本会自动安装依赖并运行统计

### 方法二：使用简化版（无需安装依赖）
1. 双击运行 `simple_token_counter.py`
2. 或在命令行中运行：`python simple_token_counter.py`

### 方法三：使用完整版（需要先安装tiktoken）
1. 安装依赖：`pip install tiktoken`
2. 运行统计：`python token_counter.py`

## 命令行参数（完整版）

```bash
python token_counter.py [目录路径] [选项]

选项:
  --encoding ENCODING    指定tiktoken编码 (默认: cl100k_base)
  --extensions EXT [EXT ...]  指定文件扩展名 (默认: .txt .md)
  --output OUTPUT        指定输出文件路径
```

### 示例：
```bash
# 统计销售培训系统文件夹
python token_counter.py 销售培训系统

# 只统计txt文件
python token_counter.py 销售培训系统 --extensions .txt

# 使用不同编码
python token_counter.py 销售培训系统 --encoding gpt2

# 指定输出文件
python token_counter.py 销售培训系统 --output my_report.txt
```

## 输出说明

工具会生成以下信息：

1. **控制台输出**：
   - 处理进度
   - 每个文件的Token和字符统计
   - 总体统计摘要
   - Token数量最多的前10个文件

2. **文件输出**：
   - 自动生成统计报告文件
   - 包含详细的文件统计信息
   - 按Token数量排序

## Token计算说明

### 完整版 (token_counter.py)
- 使用OpenAI官方的tiktoken库
- 支持多种编码模式：
  - `cl100k_base`: GPT-4, GPT-3.5-turbo
  - `p50k_base`: GPT-3 (davinci, curie, babbage, ada)
  - `r50k_base`: GPT-3 (davinci, curie, babbage, ada)

### 简化版 (simple_token_counter.py)
- 中文字符：1个字符 = 1个Token
- 英文单词：1个单词 ≈ 1.3个Token
- 适合快速估算，精度略低于tiktoken

## 注意事项

1. 确保Python环境已正确安装
2. 销售培训系统文件夹需要在当前目录下
3. 如果遇到编码问题，工具会自动尝试多种编码方式
4. 大文件可能需要较长处理时间

## 系统要求

- Python 3.6+
- tiktoken库（仅完整版需要）
- Windows/Linux/macOS

## 故障排除

### 问题1：找不到Python
- 确保已安装Python并添加到系统PATH

### 问题2：tiktoken安装失败
- 尝试使用：`pip install --upgrade pip`
- 然后：`pip install tiktoken`

### 问题3：文件编码错误
- 工具会自动尝试多种编码
- 如果仍有问题，请检查文件是否损坏

### 问题4：找不到销售培训系统文件夹
- 确保文件夹名称正确
- 确保文件夹在当前目录下
